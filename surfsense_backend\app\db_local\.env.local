# Environment configuration for SurfSense Local Database
# Copy this file to .env and customize for your environment

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database mode: 'local' for FAISS+SQLite, 'postgresql' for original setup
DATABASE_MODE=local

# Local database data directory (absolute path or relative to app root)
LOCAL_DB_DATA_DIR=./data/local_db

# SQLite database file name
SQLITE_DB_FILE=surfsense.db

# =============================================================================
# FAISS VECTOR INDEX CONFIGURATION
# =============================================================================

# FAISS index type: HNSW (recommended), IVF, or Flat
FAISS_INDEX_TYPE=HNSW

# HNSW parameters (for FAISS_INDEX_TYPE=HNSW)
FAISS_HNSW_M=32                    # Number of connections per node (16-64)
FAISS_HNSW_EF_CONSTRUCTION=200     # Size of dynamic candidate list (100-800)
FAISS_HNSW_EF_SEARCH=100          # Search parameter (50-500)

# IVF parameters (for FAISS_INDEX_TYPE=IVF)
FAISS_IVF_NLIST=100               # Number of clusters
FAISS_IVF_NPROBE=10               # Number of clusters to search

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Batch size for database operations
LOCAL_DB_BATCH_SIZE=100

# Maximum memory usage in MB
LOCAL_DB_MAX_MEMORY_MB=2048

# Enable index persistence to disk
LOCAL_DB_PERSIST_INDICES=true

# Enable performance logging
LOCAL_DB_PERF_LOG=true

# Number of threads for parallel operations
LOCAL_DB_NUM_THREADS=4

# =============================================================================
# SEARCH CONFIGURATION
# =============================================================================

# Default number of results to return
LOCAL_DB_DEFAULT_SEARCH_K=10

# Reciprocal Rank Fusion parameter (higher = more conservative)
LOCAL_DB_RRF_K=60

# Weight for vector search in hybrid search (0.0-1.0)
LOCAL_DB_VECTOR_WEIGHT=0.7

# Weight for full-text search in hybrid search (0.0-1.0)
LOCAL_DB_FTS_WEIGHT=0.3

# =============================================================================
# MIGRATION SETTINGS
# =============================================================================

# Automatically migrate from PostgreSQL on startup
AUTO_MIGRATE_TO_LOCAL=false

# PostgreSQL connection for migration (only needed if migrating)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=surfsense
POSTGRES_USER=surfsense
POSTGRES_PASSWORD=your_password

# Migration backup directory
MIGRATION_BACKUP_DIR=./data/migration_backup

# Validate data after migration
MIGRATION_VALIDATE_DATA=true

# =============================================================================
# EMBEDDING MODEL CONFIGURATION
# =============================================================================

# Embedding model name
EMBEDDING_MODEL=mixedbread-ai/mxbai-embed-large-v1

# Device for embedding model: cpu, cuda, mps
EMBEDDING_DEVICE=cpu

# Embedding dimension (auto-detected if not specified)
EMBEDDING_DIMENSION=1024

# Batch size for embedding generation
EMBEDDING_BATCH_SIZE=32

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level: DEBUG, INFO, WARNING, ERROR
LOCAL_DB_LOG_LEVEL=INFO
LOG_LEVEL=INFO

# Log file path
LOG_FILE=./logs/surfsense_local.log

# Enable structured logging
STRUCTURED_LOGGING=true

# =============================================================================
# API CONFIGURATION
# =============================================================================

# API settings
API_V1_STR=/api/v1
PROJECT_NAME=SurfSense Local
VERSION=1.0.0

# CORS settings
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Secret key for JWT tokens (generate a secure random key)
SECRET_KEY=your-super-secret-key-change-this-in-production

# JWT token expiration time in minutes
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Password hashing algorithm
PASSWORD_HASH_ALGORITHM=bcrypt

# =============================================================================
# REDIS CONFIGURATION (OPTIONAL)
# =============================================================================

# Redis for caching (optional, improves performance)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Cache TTL in seconds
CACHE_TTL=3600

# =============================================================================
# MONITORING CONFIGURATION (OPTIONAL)
# =============================================================================

# Enable Prometheus metrics
ENABLE_METRICS=true

# Metrics endpoint
METRICS_PATH=/metrics

# Health check endpoint
HEALTH_CHECK_PATH=/health

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable debug mode
DEBUG=false

# Enable auto-reload (development only)
RELOAD=false

# Enable API documentation
ENABLE_DOCS=true

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

# Number of worker processes (for production deployment)
WORKERS=1

# Maximum number of concurrent requests
MAX_REQUESTS=1000

# Request timeout in seconds
REQUEST_TIMEOUT=30

# =============================================================================
# EXAMPLE VALUES FOR DIFFERENT ENVIRONMENTS
# =============================================================================

# Development Environment:
# DATABASE_MODE=local
# LOCAL_DB_MAX_MEMORY_MB=1024
# DEBUG=true
# RELOAD=true

# Production Environment:
# DATABASE_MODE=local
# LOCAL_DB_MAX_MEMORY_MB=4096
# LOCAL_DB_NUM_THREADS=8
# WORKERS=4
# DEBUG=false

# High Performance Environment:
# FAISS_INDEX_TYPE=HNSW
# FAISS_HNSW_M=64
# FAISS_HNSW_EF_CONSTRUCTION=400
# FAISS_HNSW_EF_SEARCH=200
# LOCAL_DB_MAX_MEMORY_MB=8192
