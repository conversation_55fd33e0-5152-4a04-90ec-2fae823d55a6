# Dockerfile for SurfSense with Local Database (FAISS + SQLite)
# Optimized for performance and minimal dependencies

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libsqlite3-dev \
    sqlite3 \
    # FAISS dependencies
    libblas-dev \
    liblapack-dev \
    libopenblas-dev \
    # Performance monitoring
    htop \
    # Cleanup
    && rm -rf /var/lib/apt/lists/*

# Create data directories
RUN mkdir -p /app/data/local_db /app/data/migration_backup /app/logs

# Copy requirements
COPY requirements.local.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.local.txt

# Copy application code
COPY . .

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash surfsense && \
    chown -R surfsense:surfsense /app

# Switch to non-root user
USER surfsense

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV DATABASE_MODE=local

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
