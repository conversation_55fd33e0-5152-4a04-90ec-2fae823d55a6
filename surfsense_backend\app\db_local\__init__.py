# Local Database System with FAISS + SQLite
# This module provides a high-performance local alternative to PostgreSQL + pgvector

from .faiss_manager import FAISSIndexManager
from .sqlite_models import *
from .hybrid_search import LocalHybridSearchService
from .migration import DatabaseMigrator

__all__ = [
    'FAISSIndexManager',
    'LocalHybridSearchService', 
    'DatabaseMigrator',
    'LocalUser',
    'LocalSearchSpace',
    'LocalDocument',
    'LocalChunk',
    'LocalChat',
    'LocalPodcast',
    'LocalLLMConfig',
    'LocalSearchSourceConnector',
    'get_local_session',
    'init_local_database'
]
