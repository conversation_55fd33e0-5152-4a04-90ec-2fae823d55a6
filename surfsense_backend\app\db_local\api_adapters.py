"""
API adapters that provide seamless integration with existing FastAPI routes.
These adapters maintain the same interface as PostgreSQL-based services but use local database.
"""

from typing import List, Dict, Any, Optional, AsyncGenerator
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from .config import db_mode_config, get_database_session, get_document_retriever, get_chunk_retriever
from .service_adapters import local_db_service
from .sqlite_models import LocalUser, LocalSearchSpace, LocalDocument, LocalChunk, LocalChat

# Import original dependencies for compatibility
from app.dependencies import get_current_active_user, get_async_session as get_pg_session
from app.db import User as PgUser

logger = logging.getLogger(__name__)

# Database session dependency that works with both modes
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Database session dependency that automatically uses the correct database based on configuration.
    This replaces get_async_session in routes when using local database.
    """
    if db_mode_config.is_local_mode():
        async with local_db_service.get_session() as session:
            yield session
    else:
        async with get_pg_session() as session:
            yield session

# User authentication adapter
async def get_current_user_adapter(session: AsyncSession = Depends(get_db_session)) -> Any:
    """
    User authentication adapter that works with both database modes.
    Returns user object compatible with existing code.
    """
    if db_mode_config.is_local_mode():
        # For demo purposes, return a demo user
        # In production, this would integrate with your authentication system
        demo_user = await local_db_service.get_user_by_id(session, "demo_user_123")
        if not demo_user:
            # Create demo user if it doesn't exist
            demo_user = await local_db_service.create_user(session, {
                'id': 'demo_user_123',
                'email': '<EMAIL>',
                'hashed_password': 'demo_hash',
                'is_active': True,
                'is_verified': True
            })
        return demo_user
    else:
        # Use original authentication
        return await get_current_active_user(session)

# Search Space Service Adapter
class SearchSpaceServiceAdapter:
    """Adapter for search space operations that works with both database modes"""
    
    @staticmethod
    async def get_user_search_spaces(user_id: str, session: AsyncSession) -> List[Any]:
        """Get all search spaces for a user"""
        if db_mode_config.is_local_mode():
            return await local_db_service.get_search_spaces_by_user(session, user_id)
        else:
            # Use original PostgreSQL logic
            from app.services.search_spaces import get_search_spaces_by_user_id
            return await get_search_spaces_by_user_id(session, user_id)
    
    @staticmethod
    async def create_search_space(user_id: str, name: str, description: str, session: AsyncSession) -> Any:
        """Create a new search space"""
        if db_mode_config.is_local_mode():
            return await local_db_service.create_search_space(session, {
                'name': name,
                'description': description,
                'user_id': user_id
            })
        else:
            # Use original PostgreSQL logic
            from app.services.search_spaces import create_search_space
            return await create_search_space(session, user_id, name, description)
    
    @staticmethod
    async def get_search_space_by_id(search_space_id: int, user_id: str, session: AsyncSession) -> Optional[Any]:
        """Get search space by ID with ownership check"""
        if db_mode_config.is_local_mode():
            return await local_db_service.get_search_space_by_id(session, search_space_id, user_id)
        else:
            # Use original PostgreSQL logic
            from app.services.search_spaces import get_search_space_by_id
            return await get_search_space_by_id(session, search_space_id, user_id)

# Document Service Adapter
class DocumentServiceAdapter:
    """Adapter for document operations that works with both database modes"""
    
    @staticmethod
    async def get_documents_by_search_space(search_space_id: int, skip: int, limit: int, session: AsyncSession) -> List[Any]:
        """Get documents in a search space"""
        if db_mode_config.is_local_mode():
            return await local_db_service.get_documents_by_search_space(session, search_space_id, skip, limit)
        else:
            # Use original PostgreSQL logic
            from app.services.documents import get_documents_by_search_space_id
            return await get_documents_by_search_space_id(session, search_space_id, skip, limit)
    
    @staticmethod
    async def create_document(search_space_id: int, title: str, content: str, document_type: str, 
                            session: AsyncSession, metadata: Optional[Dict] = None) -> Any:
        """Create a new document"""
        if db_mode_config.is_local_mode():
            import hashlib
            content_hash = hashlib.md5(content.encode()).hexdigest()
            
            return await local_db_service.create_document(session, {
                'title': title,
                'content': content,
                'document_type': document_type,
                'search_space_id': search_space_id,
                'content_hash': content_hash,
                'document_metadata': metadata
            })
        else:
            # Use original PostgreSQL logic
            from app.services.documents import create_document
            return await create_document(session, search_space_id, title, content, document_type, metadata)
    
    @staticmethod
    async def delete_document(document_id: int, session: AsyncSession) -> bool:
        """Delete a document"""
        if db_mode_config.is_local_mode():
            return await local_db_service.delete_document(session, document_id)
        else:
            # Use original PostgreSQL logic
            from app.services.documents import delete_document
            return await delete_document(session, document_id)
    
    @staticmethod
    async def get_document_by_id(document_id: int, session: AsyncSession) -> Optional[Any]:
        """Get document by ID"""
        if db_mode_config.is_local_mode():
            return await local_db_service.get_document_by_id(session, document_id)
        else:
            # Use original PostgreSQL logic
            from app.services.documents import get_document_by_id
            return await get_document_by_id(session, document_id)

# Search Service Adapter
class SearchServiceAdapter:
    """Adapter for search operations that works with both database modes"""
    
    @staticmethod
    async def hybrid_search_documents(query: str, top_k: int, user_id: str, search_space_id: Optional[int], 
                                    document_type: Optional[str], session: AsyncSession) -> List[Dict[str, Any]]:
        """Perform hybrid search on documents"""
        if db_mode_config.is_local_mode():
            return await local_db_service.hybrid_search_documents(
                session, query, top_k, user_id, search_space_id, document_type
            )
        else:
            # Use original PostgreSQL retriever
            retriever = get_document_retriever(session)
            return await retriever.hybrid_search(query, top_k, user_id, search_space_id, document_type)
    
    @staticmethod
    async def hybrid_search_chunks(query: str, top_k: int, user_id: str, search_space_id: Optional[int], 
                                 document_type: Optional[str], session: AsyncSession) -> List[Dict[str, Any]]:
        """Perform hybrid search on chunks"""
        if db_mode_config.is_local_mode():
            return await local_db_service.hybrid_search_chunks(
                session, query, top_k, user_id, search_space_id, document_type
            )
        else:
            # Use original PostgreSQL retriever
            retriever = get_chunk_retriever(session)
            return await retriever.hybrid_search(query, top_k, user_id, search_space_id, document_type)
    
    @staticmethod
    async def vector_search_documents(query: str, top_k: int, user_id: str, search_space_id: Optional[int], 
                                    session: AsyncSession) -> List[Any]:
        """Perform vector search on documents"""
        if db_mode_config.is_local_mode():
            return await local_db_service.vector_search_documents(query, top_k, user_id, search_space_id)
        else:
            # Use original PostgreSQL retriever
            retriever = get_document_retriever(session)
            return await retriever.vector_search(query, top_k, user_id, search_space_id)

# Chat Service Adapter
class ChatServiceAdapter:
    """Adapter for chat operations that works with both database modes"""
    
    @staticmethod
    async def create_chat(search_space_id: int, title: str, chat_type: str, session: AsyncSession) -> Any:
        """Create a new chat"""
        if db_mode_config.is_local_mode():
            return await local_db_service.create_chat(session, {
                'title': title,
                'type': chat_type,
                'search_space_id': search_space_id,
                'messages': '[]',
                'initial_connectors': None
            })
        else:
            # Use original PostgreSQL logic
            from app.services.chats import create_chat
            return await create_chat(session, search_space_id, title, chat_type)
    
    @staticmethod
    async def get_chats_by_search_space(search_space_id: int, session: AsyncSession) -> List[Any]:
        """Get chats in a search space"""
        if db_mode_config.is_local_mode():
            return await local_db_service.get_chats_by_search_space(session, search_space_id)
        else:
            # Use original PostgreSQL logic
            from app.services.chats import get_chats_by_search_space_id
            return await get_chats_by_search_space_id(session, search_space_id)

# Response format adapters to ensure API compatibility
class ResponseFormatAdapter:
    """Adapter to ensure response formats match original API"""
    
    @staticmethod
    def format_document_response(document: Any) -> Dict[str, Any]:
        """Format document for API response"""
        if db_mode_config.is_local_mode():
            # Convert LocalDocument to expected format
            return {
                'id': document.id,
                'title': document.title,
                'content': document.content,
                'document_type': document.document_type,
                'search_space_id': document.search_space_id,
                'created_at': document.created_at.isoformat() if document.created_at else None,
                'updated_at': document.updated_at.isoformat() if document.updated_at else None,
                'content_hash': document.content_hash,
                'document_metadata': document.get_metadata() if hasattr(document, 'get_metadata') else None
            }
        else:
            # Use original format
            return {
                'id': document.id,
                'title': document.title,
                'content': document.content,
                'document_type': document.document_type.value if hasattr(document.document_type, 'value') else document.document_type,
                'search_space_id': document.search_space_id,
                'created_at': document.created_at.isoformat() if document.created_at else None,
                'updated_at': document.updated_at.isoformat() if document.updated_at else None,
                'content_hash': document.content_hash,
                'document_metadata': document.document_metadata
            }
    
    @staticmethod
    def format_search_space_response(search_space: Any) -> Dict[str, Any]:
        """Format search space for API response"""
        return {
            'id': search_space.id,
            'name': search_space.name,
            'description': search_space.description,
            'user_id': search_space.user_id,
            'created_at': search_space.created_at.isoformat() if search_space.created_at else None,
            'updated_at': search_space.updated_at.isoformat() if search_space.updated_at else None
        }
    
    @staticmethod
    def format_search_results(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format search results for API response"""
        formatted_results = []
        
        for result in results:
            if 'item' in result:
                # Local database format
                item = result['item']
                formatted_result = {
                    'score': result['score'],
                    'vector_score': result.get('vector_score', 0.0),
                    'fts_score': result.get('fts_score', 0.0)
                }
                
                # Add item data based on type
                if hasattr(item, 'title'):  # Document
                    formatted_result.update(ResponseFormatAdapter.format_document_response(item))
                else:  # Chunk
                    formatted_result.update({
                        'id': item.id,
                        'content': item.content,
                        'document_id': item.document_id,
                        'created_at': item.created_at.isoformat() if item.created_at else None
                    })
                
                formatted_results.append(formatted_result)
            else:
                # Original PostgreSQL format - pass through
                formatted_results.append(result)
        
        return formatted_results

# Export the adapters for use in routes
__all__ = [
    'get_db_session',
    'get_current_user_adapter', 
    'SearchSpaceServiceAdapter',
    'DocumentServiceAdapter',
    'SearchServiceAdapter',
    'ChatServiceAdapter',
    'ResponseFormatAdapter'
]
