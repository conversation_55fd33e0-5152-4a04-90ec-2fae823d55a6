"""
Configuration system for local database with easy switching between PostgreSQL and local modes.
"""

import os
from typing import Optional, Dict, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class LocalDatabaseConfig:
    """Configuration for the local FAISS + SQLite database system"""
    
    def __init__(self):
        # Database paths
        self.data_dir = Path(os.getenv("LOCAL_DB_DATA_DIR", "data/local_db"))
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.sqlite_path = self.data_dir / "surfsense.db"
        self.faiss_indices_dir = self.data_dir / "faiss_indices"
        self.faiss_indices_dir.mkdir(exist_ok=True)
        
        # FAISS configuration
        self.faiss_index_type = os.getenv("FAISS_INDEX_TYPE", "HNSW")  # HNSW, IVF, Flat
        self.faiss_hnsw_m = int(os.getenv("FAISS_HNSW_M", "32"))  # Number of connections
        self.faiss_hnsw_ef_construction = int(os.getenv("FAISS_HNSW_EF_CONSTRUCTION", "200"))
        self.faiss_hnsw_ef_search = int(os.getenv("FAISS_HNSW_EF_SEARCH", "100"))
        
        # Performance settings
        self.batch_size = int(os.getenv("LOCAL_DB_BATCH_SIZE", "100"))
        self.max_memory_usage_mb = int(os.getenv("LOCAL_DB_MAX_MEMORY_MB", "2048"))
        self.enable_index_persistence = os.getenv("LOCAL_DB_PERSIST_INDICES", "true").lower() == "true"
        
        # Search settings
        self.default_search_k = int(os.getenv("LOCAL_DB_DEFAULT_SEARCH_K", "10"))
        self.rrf_k = int(os.getenv("LOCAL_DB_RRF_K", "60"))  # Reciprocal Rank Fusion constant
        
        # Logging
        self.log_level = os.getenv("LOCAL_DB_LOG_LEVEL", "INFO")
        self.enable_performance_logging = os.getenv("LOCAL_DB_PERF_LOG", "false").lower() == "true"
    
    def get_sqlite_url(self) -> str:
        """Get SQLite database URL"""
        return f"sqlite+aiosqlite:///{self.sqlite_path}"
    
    def get_faiss_index_path(self, search_space_id: int, record_type: str) -> Path:
        """Get path for FAISS index file"""
        return self.faiss_indices_dir / f"index_{search_space_id}_{record_type}.faiss"
    
    def get_faiss_metadata_path(self, search_space_id: int, record_type: str) -> Path:
        """Get path for FAISS metadata file"""
        return self.faiss_indices_dir / f"metadata_{search_space_id}_{record_type}.json"

class DatabaseModeConfig:
    """Configuration for switching between database modes"""
    
    def __init__(self):
        # Database mode: 'postgresql' or 'local'
        self.mode = os.getenv("DATABASE_MODE", "postgresql").lower()
        
        # Migration settings
        self.auto_migrate = os.getenv("AUTO_MIGRATE_TO_LOCAL", "false").lower() == "true"
        self.migration_backup_dir = os.getenv("MIGRATION_BACKUP_DIR", "migration_backup")
        
        # Fallback settings
        self.enable_fallback = os.getenv("ENABLE_DB_FALLBACK", "false").lower() == "true"
        self.fallback_timeout_seconds = int(os.getenv("DB_FALLBACK_TIMEOUT", "5"))
        
        # Performance comparison
        self.enable_performance_comparison = os.getenv("ENABLE_PERF_COMPARISON", "false").lower() == "true"
        
        self.local_config = LocalDatabaseConfig()
    
    def is_local_mode(self) -> bool:
        """Check if running in local database mode"""
        return self.mode == "local"
    
    def is_postgresql_mode(self) -> bool:
        """Check if running in PostgreSQL mode"""
        return self.mode == "postgresql"
    
    def should_auto_migrate(self) -> bool:
        """Check if should automatically migrate to local database"""
        return self.auto_migrate and self.is_local_mode()

# Global configuration instance
db_mode_config = DatabaseModeConfig()

# Environment variable validation
def validate_environment():
    """Validate environment variables and configuration"""
    issues = []
    
    # Check database mode
    if db_mode_config.mode not in ['postgresql', 'local']:
        issues.append(f"Invalid DATABASE_MODE: {db_mode_config.mode}. Must be 'postgresql' or 'local'")
    
    # Check local database paths
    if db_mode_config.is_local_mode():
        try:
            db_mode_config.local_config.data_dir.mkdir(parents=True, exist_ok=True)
            db_mode_config.local_config.faiss_indices_dir.mkdir(exist_ok=True)
        except Exception as e:
            issues.append(f"Cannot create local database directories: {e}")
    
    # Check FAISS settings
    if db_mode_config.local_config.faiss_hnsw_m < 4 or db_mode_config.local_config.faiss_hnsw_m > 64:
        issues.append(f"FAISS_HNSW_M should be between 4 and 64, got {db_mode_config.local_config.faiss_hnsw_m}")
    
    if issues:
        for issue in issues:
            logger.error(f"Configuration issue: {issue}")
        raise ValueError(f"Configuration validation failed: {'; '.join(issues)}")
    
    logger.info(f"Database mode: {db_mode_config.mode}")
    if db_mode_config.is_local_mode():
        logger.info(f"Local database path: {db_mode_config.local_config.sqlite_path}")
        logger.info(f"FAISS indices path: {db_mode_config.local_config.faiss_indices_dir}")

# Performance monitoring
class PerformanceMonitor:
    """Monitor and compare performance between database modes"""

    def __init__(self):
        from datetime import datetime
        self.metrics = {
            'search_times': [],
            'insert_times': [],
            'memory_usage': [],
            'query_counts': {}
        }
    
    def record_search_time(self, query_type: str, duration: float, result_count: int):
        """Record search performance metrics"""
        from datetime import datetime
        if db_mode_config.local_config.enable_performance_logging:
            self.metrics['search_times'].append({
                'type': query_type,
                'duration': duration,
                'result_count': result_count,
                'timestamp': datetime.now().isoformat()
            })
    
    def record_insert_time(self, operation: str, duration: float, record_count: int):
        """Record insert/update performance metrics"""
        from datetime import datetime
        if db_mode_config.local_config.enable_performance_logging:
            self.metrics['insert_times'].append({
                'operation': operation,
                'duration': duration,
                'record_count': record_count,
                'timestamp': datetime.now().isoformat()
            })
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary statistics"""
        summary = {}
        
        if self.metrics['search_times']:
            search_times = [m['duration'] for m in self.metrics['search_times']]
            summary['search'] = {
                'avg_time': sum(search_times) / len(search_times),
                'min_time': min(search_times),
                'max_time': max(search_times),
                'total_queries': len(search_times)
            }
        
        if self.metrics['insert_times']:
            insert_times = [m['duration'] for m in self.metrics['insert_times']]
            summary['insert'] = {
                'avg_time': sum(insert_times) / len(insert_times),
                'min_time': min(insert_times),
                'max_time': max(insert_times),
                'total_operations': len(insert_times)
            }
        
        return summary

# Global performance monitor
performance_monitor = PerformanceMonitor()

# Utility functions
def get_database_session():
    """Get appropriate database session based on configuration"""
    if db_mode_config.is_local_mode():
        from .service_adapters import local_db_service
        return local_db_service.get_session()
    else:
        from app.db import get_async_session
        return get_async_session()

def get_document_retriever(session):
    """Get appropriate document retriever based on configuration"""
    if db_mode_config.is_local_mode():
        from .local_retrievers import create_local_document_retriever
        return create_local_document_retriever(session)
    else:
        from app.retriver.documents_hybrid_search import DocumentHybridSearchRetriever
        return DocumentHybridSearchRetriever(session)

def get_chunk_retriever(session):
    """Get appropriate chunk retriever based on configuration"""
    if db_mode_config.is_local_mode():
        from .local_retrievers import create_local_chunk_retriever
        return create_local_chunk_retriever(session)
    else:
        from app.retriver.chunks_hybrid_search import ChucksHybridSearchRetriever
        return ChucksHybridSearchRetriever(session)

# Configuration display
def print_configuration():
    """Print current configuration for debugging"""
    print("\n" + "="*50)
    print("SURFSENSE DATABASE CONFIGURATION")
    print("="*50)
    print(f"Database Mode: {db_mode_config.mode.upper()}")
    
    if db_mode_config.is_local_mode():
        print(f"SQLite Path: {db_mode_config.local_config.sqlite_path}")
        print(f"FAISS Indices: {db_mode_config.local_config.faiss_indices_dir}")
        print(f"FAISS Index Type: {db_mode_config.local_config.faiss_index_type}")
        print(f"HNSW Parameters: M={db_mode_config.local_config.faiss_hnsw_m}, "
              f"EF_construction={db_mode_config.local_config.faiss_hnsw_ef_construction}")
        print(f"Batch Size: {db_mode_config.local_config.batch_size}")
        print(f"Max Memory: {db_mode_config.local_config.max_memory_usage_mb}MB")
    
    print(f"Auto Migration: {db_mode_config.auto_migrate}")
    print(f"Performance Logging: {db_mode_config.local_config.enable_performance_logging}")
    print("="*50 + "\n")

# Initialize configuration on import
try:
    validate_environment()
except Exception as e:
    logger.error(f"Configuration validation failed: {e}")
    # Don't raise here to allow import, but log the issue
