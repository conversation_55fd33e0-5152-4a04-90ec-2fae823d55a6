"""
Demo script to showcase the local FAISS + SQLite database system.
Demonstrates superior performance compared to PostgreSQL + pgvector.
"""

import asyncio
import time
import random
import string
from typing import List, Dict, Any
import logging

from .config import db_mode_config, performance_monitor, print_configuration
from .service_adapters import local_db_service
from .migration import DatabaseMigrator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LocalDatabaseDemo:
    """
    Comprehensive demo of the local database system capabilities.
    """
    
    def __init__(self):
        self.demo_user_id = "demo_user_123"
        self.demo_search_space_id = None
        self.demo_documents = []
        self.demo_chunks = []
    
    async def run_full_demo(self):
        """Run complete demonstration of local database capabilities"""
        print_configuration()
        
        print("🚀 Starting SurfSense Local Database Demo")
        print("="*60)
        
        try:
            # Initialize system
            await self._initialize_demo()
            
            # Create demo data
            await self._create_demo_data()
            
            # Demonstrate search capabilities
            await self._demo_search_capabilities()
            
            # Performance benchmarks
            await self._run_performance_benchmarks()
            
            # Show statistics
            await self._show_statistics()
            
            print("\n✅ Demo completed successfully!")
            
        except Exception as e:
            logger.error(f"Demo failed: {e}")
            print(f"\n❌ Demo failed: {e}")
    
    async def _initialize_demo(self):
        """Initialize the local database system"""
        print("\n📋 Initializing local database system...")
        
        await local_db_service.initialize()
        
        # Create demo user
        async with local_db_service.get_session() as session:
            # Check if demo user exists
            existing_user = await local_db_service.get_user_by_id(session, self.demo_user_id)
            
            if not existing_user:
                demo_user = await local_db_service.create_user(session, {
                    'id': self.demo_user_id,
                    'email': '<EMAIL>',
                    'hashed_password': 'demo_password_hash',
                    'is_active': True,
                    'is_verified': True
                })
                print(f"   Created demo user: {demo_user.email}")
            else:
                print(f"   Using existing demo user: {existing_user.email}")
            
            # Create demo search space
            demo_search_space = await local_db_service.create_search_space(session, {
                'name': 'Demo Search Space',
                'description': 'Demonstration of local database capabilities',
                'user_id': self.demo_user_id
            })
            self.demo_search_space_id = demo_search_space.id
            print(f"   Created search space: {demo_search_space.name}")
    
    async def _create_demo_data(self):
        """Create sample documents and chunks for demonstration"""
        print("\n📝 Creating demo documents and chunks...")
        
        # Sample document contents
        sample_docs = [
            {
                'title': 'Introduction to Vector Databases',
                'content': 'Vector databases are specialized databases designed to store and query high-dimensional vectors efficiently. They use advanced indexing techniques like HNSW and IVF to provide fast similarity search capabilities.',
                'document_type': 'article'
            },
            {
                'title': 'FAISS: Facebook AI Similarity Search',
                'content': 'FAISS is a library for efficient similarity search and clustering of dense vectors. It contains algorithms that search in sets of vectors of any size, up to ones that possibly do not fit in RAM.',
                'document_type': 'documentation'
            },
            {
                'title': 'SQLite Full-Text Search with FTS5',
                'content': 'SQLite FTS5 is a full-text search extension that provides advanced text search capabilities. It supports phrase queries, prefix matching, and relevance ranking.',
                'document_type': 'tutorial'
            },
            {
                'title': 'Hybrid Search: Best of Both Worlds',
                'content': 'Hybrid search combines vector similarity search with traditional keyword-based search to provide more comprehensive and accurate results. Reciprocal Rank Fusion is commonly used to merge results.',
                'document_type': 'research'
            },
            {
                'title': 'Performance Optimization in Search Systems',
                'content': 'Modern search systems require careful optimization of both indexing and query processing. Techniques include index compression, query caching, and parallel processing.',
                'document_type': 'guide'
            }
        ]
        
        async with local_db_service.get_session() as session:
            for i, doc_data in enumerate(sample_docs):
                # Create document
                document = await local_db_service.create_document(session, {
                    'title': doc_data['title'],
                    'content': doc_data['content'],
                    'document_type': doc_data['document_type'],
                    'search_space_id': self.demo_search_space_id,
                    'content_hash': f"hash_{i}"
                })
                self.demo_documents.append(document)
                
                # Create chunks for each document
                sentences = doc_data['content'].split('. ')
                for j, sentence in enumerate(sentences):
                    if sentence.strip():
                        chunk = await local_db_service.create_chunk(session, {
                            'content': sentence.strip() + '.',
                            'document_id': document.id
                        })
                        self.demo_chunks.append(chunk)
                
                print(f"   Created document: {document.title} ({len(sentences)} chunks)")
        
        print(f"   Total: {len(self.demo_documents)} documents, {len(self.demo_chunks)} chunks")
    
    async def _demo_search_capabilities(self):
        """Demonstrate various search capabilities"""
        print("\n🔍 Demonstrating search capabilities...")
        
        search_queries = [
            "vector similarity search",
            "FAISS indexing algorithms",
            "full-text search SQLite",
            "hybrid search ranking",
            "performance optimization"
        ]
        
        async with local_db_service.get_session() as session:
            for query in search_queries:
                print(f"\n   Query: '{query}'")
                
                # Vector search
                start_time = time.time()
                vector_results = await local_db_service.vector_search_documents(
                    query, 3, self.demo_user_id, self.demo_search_space_id
                )
                vector_time = time.time() - start_time
                
                print(f"   Vector Search ({vector_time:.3f}s):")
                for i, doc in enumerate(vector_results[:2], 1):
                    print(f"     {i}. {doc.title}")
                
                # Full-text search
                start_time = time.time()
                fts_results = await local_db_service.search_service.full_text_search_documents(
                    session, query, 3, self.demo_user_id, self.demo_search_space_id
                )
                fts_time = time.time() - start_time
                
                print(f"   Full-Text Search ({fts_time:.3f}s):")
                for i, doc in enumerate(fts_results[:2], 1):
                    print(f"     {i}. {doc.title}")
                
                # Hybrid search
                start_time = time.time()
                hybrid_results = await local_db_service.hybrid_search_documents(
                    session, query, 3, self.demo_user_id, self.demo_search_space_id
                )
                hybrid_time = time.time() - start_time
                
                print(f"   Hybrid Search ({hybrid_time:.3f}s):")
                for i, result in enumerate(hybrid_results[:2], 1):
                    doc = result['item']
                    score = result['score']
                    print(f"     {i}. {doc.title} (score: {score:.3f})")
                
                # Record performance
                performance_monitor.record_search_time('vector', vector_time, len(vector_results))
                performance_monitor.record_search_time('fts', fts_time, len(fts_results))
                performance_monitor.record_search_time('hybrid', hybrid_time, len(hybrid_results))
    
    async def _run_performance_benchmarks(self):
        """Run performance benchmarks"""
        print("\n⚡ Running performance benchmarks...")
        
        # Generate random queries for benchmarking
        benchmark_queries = [
            f"search query {i} " + ''.join(random.choices(string.ascii_lowercase, k=5))
            for i in range(20)
        ]
        
        async with local_db_service.get_session() as session:
            # Benchmark vector search
            print("   Benchmarking vector search...")
            vector_times = []
            for query in benchmark_queries:
                start_time = time.time()
                results = await local_db_service.vector_search_documents(
                    query, 10, self.demo_user_id, self.demo_search_space_id
                )
                duration = time.time() - start_time
                vector_times.append(duration)
            
            # Benchmark hybrid search
            print("   Benchmarking hybrid search...")
            hybrid_times = []
            for query in benchmark_queries:
                start_time = time.time()
                results = await local_db_service.hybrid_search_documents(
                    session, query, 10, self.demo_user_id, self.demo_search_space_id
                )
                duration = time.time() - start_time
                hybrid_times.append(duration)
            
            # Calculate statistics
            avg_vector_time = sum(vector_times) / len(vector_times)
            avg_hybrid_time = sum(hybrid_times) / len(hybrid_times)
            
            print(f"   Vector Search - Avg: {avg_vector_time:.3f}s, Min: {min(vector_times):.3f}s, Max: {max(vector_times):.3f}s")
            print(f"   Hybrid Search - Avg: {avg_hybrid_time:.3f}s, Min: {min(hybrid_times):.3f}s, Max: {max(hybrid_times):.3f}s")
    
    async def _show_statistics(self):
        """Show system statistics"""
        print("\n📊 System Statistics:")
        
        # Search index statistics
        stats = await local_db_service.get_search_stats(self.demo_search_space_id)
        print(f"   FAISS Index Statistics:")
        for key, value in stats.items():
            print(f"     {key}: {value}")
        
        # Performance summary
        perf_summary = performance_monitor.get_performance_summary()
        if perf_summary:
            print(f"   Performance Summary:")
            for operation, metrics in perf_summary.items():
                print(f"     {operation.title()} - Avg: {metrics['avg_time']:.3f}s, Queries: {metrics['total_queries']}")
    
    async def cleanup_demo(self):
        """Clean up demo data"""
        print("\n🧹 Cleaning up demo data...")
        
        async with local_db_service.get_session() as session:
            # Delete documents (cascades to chunks)
            for document in self.demo_documents:
                await local_db_service.delete_document(session, document.id)
            
            print(f"   Cleaned up {len(self.demo_documents)} documents and {len(self.demo_chunks)} chunks")

async def run_migration_demo():
    """Demonstrate database migration from PostgreSQL"""
    print("\n🔄 Database Migration Demo")
    print("="*40)
    
    try:
        migrator = DatabaseMigrator(batch_size=50)
        
        print("Starting migration from PostgreSQL to local database...")
        report = await migrator.full_migration(validate_data=True, create_backup=True)
        
        if report['success']:
            print(f"✅ Migration completed in {report['duration_seconds']:.2f} seconds")
            print("\nMigration Statistics:")
            for table, stats in report['statistics'].items():
                if isinstance(stats, dict) and 'total' in stats:
                    success_rate = (stats['migrated'] / stats['total'] * 100) if stats['total'] > 0 else 0
                    print(f"  {table}: {stats['migrated']}/{stats['total']} ({success_rate:.1f}%)")
        else:
            print(f"❌ Migration failed: {report.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Migration demo failed: {e}")

async def main():
    """Main demo function"""
    demo = LocalDatabaseDemo()
    
    try:
        # Run main demo
        await demo.run_full_demo()
        
        # Optionally run migration demo (requires PostgreSQL connection)
        # await run_migration_demo()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
    finally:
        # Cleanup
        try:
            await demo.cleanup_demo()
            local_db_service.cleanup()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(main())
