# Docker Compose configuration for SurfSense with Local Database
# This configuration uses FAISS + SQLite instead of PostgreSQL for superior performance

version: '3.8'

services:
  surfsense-backend-local:
    build:
      context: .
      dockerfile: Dockerfile.local
    container_name: surfsense-backend-local
    ports:
      - "8000:8000"
    environment:
      # Database Configuration
      - DATABASE_MODE=local
      - LOCAL_DB_DATA_DIR=/app/data/local_db
      
      # FAISS Configuration
      - FAISS_INDEX_TYPE=HNSW
      - FAISS_HNSW_M=32
      - FAISS_HNSW_EF_CONSTRUCTION=200
      - FAISS_HNSW_EF_SEARCH=100
      
      # Performance Settings
      - LOCAL_DB_BATCH_SIZE=100
      - LOCAL_DB_MAX_MEMORY_MB=2048
      - LOCAL_DB_PERSIST_INDICES=true
      - LOCAL_DB_PERF_LOG=true
      
      # Search Settings
      - LOCAL_DB_DEFAULT_SEARCH_K=10
      - LOCAL_DB_RRF_K=60
      
      # Migration Settings
      - AUTO_MIGRATE_TO_LOCAL=false
      - MIGRAT<PERSON>_BACKUP_DIR=/app/data/migration_backup
      
      # Logging
      - LOCAL_DB_LOG_LEVEL=INFO
      - LOG_LEVEL=INFO
      
      # API Settings
      - API_V1_STR=/api/v1
      - PROJECT_NAME=SurfSense Local
      
      # Security (use your own values in production)
      - SECRET_KEY=your-secret-key-here
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      
      # Embedding Model
      - EMBEDDING_MODEL=mixedbread-ai/mxbai-embed-large-v1
      - EMBEDDING_DEVICE=cpu
      
    volumes:
      # Persistent data storage
      - surfsense_local_data:/app/data
      # Optional: Mount local data directory for development
      # - ./data:/app/data
    
    networks:
      - surfsense-local-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    restart: unless-stopped
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Optional: Redis for caching (can improve performance)
  redis-local:
    image: redis:7-alpine
    container_name: surfsense-redis-local
    ports:
      - "6379:6379"
    volumes:
      - surfsense_redis_data:/data
    networks:
      - surfsense-local-network
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    restart: unless-stopped

  # Optional: Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: surfsense-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - surfsense-local-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Optional: Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: surfsense-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - surfsense-local-network
    restart: unless-stopped

volumes:
  surfsense_local_data:
    driver: local
  surfsense_redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  surfsense-local-network:
    driver: bridge

# Performance optimized configuration
# This setup provides:
# 1. Local FAISS + SQLite database for superior search performance
# 2. Redis caching for API responses
# 3. Prometheus + Grafana monitoring
# 4. Optimized resource allocation
# 5. Health checks and automatic restarts
# 6. Persistent data storage

# To run:
# docker-compose -f docker-compose.local.yml up -d

# To migrate from PostgreSQL:
# 1. Set AUTO_MIGRATE_TO_LOCAL=true
# 2. Ensure PostgreSQL connection is available during first startup
# 3. Monitor logs for migration progress

# Performance tuning:
# - Increase LOCAL_DB_MAX_MEMORY_MB for larger datasets
# - Adjust FAISS_HNSW_M and FAISS_HNSW_EF_SEARCH for speed vs accuracy tradeoff
# - Use SSD storage for best performance
# - Allocate sufficient RAM (recommended: 4GB+ for production)
