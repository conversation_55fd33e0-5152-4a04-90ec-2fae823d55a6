"""
Example routes showing how to integrate the local database system with existing FastAPI routes.
These examples demonstrate how to modify existing routes to use the local database adapters.
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from .api_adapters import (
    get_db_session, get_current_user_adapter,
    SearchSpaceServiceAdapter, DocumentServiceAdapter, 
    SearchServiceAdapter, ResponseFormatAdapter
)
from .config import db_mode_config

# Create router
router = APIRouter(prefix="/api/v1/local", tags=["Local Database Demo"])

# Pydantic models for requests/responses
class CreateSearchSpaceRequest(BaseModel):
    name: str
    description: Optional[str] = None

class CreateDocumentRequest(BaseModel):
    title: str
    content: str
    document_type: str
    metadata: Optional[Dict[str, Any]] = None

class SearchRequest(BaseModel):
    query: str
    top_k: Optional[int] = 10
    search_space_id: Optional[int] = None
    document_type: Optional[str] = None

# Search Spaces Routes
@router.get("/search-spaces")
async def get_search_spaces(
    current_user = Depends(get_current_user_adapter),
    session: AsyncSession = Depends(get_db_session)
):
    """Get all search spaces for the current user"""
    try:
        search_spaces = await SearchSpaceServiceAdapter.get_user_search_spaces(
            str(current_user.id), session
        )
        
        return {
            "search_spaces": [
                ResponseFormatAdapter.format_search_space_response(space) 
                for space in search_spaces
            ],
            "database_mode": db_mode_config.mode
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve search spaces: {str(e)}"
        )

@router.post("/search-spaces")
async def create_search_space(
    request: CreateSearchSpaceRequest,
    current_user = Depends(get_current_user_adapter),
    session: AsyncSession = Depends(get_db_session)
):
    """Create a new search space"""
    try:
        search_space = await SearchSpaceServiceAdapter.create_search_space(
            str(current_user.id), request.name, request.description or "", session
        )
        
        return {
            "search_space": ResponseFormatAdapter.format_search_space_response(search_space),
            "message": f"Search space created using {db_mode_config.mode} database"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create search space: {str(e)}"
        )

@router.get("/search-spaces/{search_space_id}")
async def get_search_space(
    search_space_id: int,
    current_user = Depends(get_current_user_adapter),
    session: AsyncSession = Depends(get_db_session)
):
    """Get a specific search space"""
    try:
        search_space = await SearchSpaceServiceAdapter.get_search_space_by_id(
            search_space_id, str(current_user.id), session
        )
        
        if not search_space:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Search space not found"
            )
        
        return {
            "search_space": ResponseFormatAdapter.format_search_space_response(search_space),
            "database_mode": db_mode_config.mode
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve search space: {str(e)}"
        )

# Documents Routes
@router.get("/search-spaces/{search_space_id}/documents")
async def get_documents(
    search_space_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user = Depends(get_current_user_adapter),
    session: AsyncSession = Depends(get_db_session)
):
    """Get documents in a search space"""
    try:
        # Verify search space ownership
        search_space = await SearchSpaceServiceAdapter.get_search_space_by_id(
            search_space_id, str(current_user.id), session
        )
        if not search_space:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Search space not found"
            )
        
        documents = await DocumentServiceAdapter.get_documents_by_search_space(
            search_space_id, skip, limit, session
        )
        
        return {
            "documents": [
                ResponseFormatAdapter.format_document_response(doc) 
                for doc in documents
            ],
            "total": len(documents),
            "skip": skip,
            "limit": limit,
            "database_mode": db_mode_config.mode
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve documents: {str(e)}"
        )

@router.post("/search-spaces/{search_space_id}/documents")
async def create_document(
    search_space_id: int,
    request: CreateDocumentRequest,
    current_user = Depends(get_current_user_adapter),
    session: AsyncSession = Depends(get_db_session)
):
    """Create a new document in a search space"""
    try:
        # Verify search space ownership
        search_space = await SearchSpaceServiceAdapter.get_search_space_by_id(
            search_space_id, str(current_user.id), session
        )
        if not search_space:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Search space not found"
            )
        
        document = await DocumentServiceAdapter.create_document(
            search_space_id, request.title, request.content, 
            request.document_type, session, request.metadata
        )
        
        return {
            "document": ResponseFormatAdapter.format_document_response(document),
            "message": f"Document created using {db_mode_config.mode} database"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create document: {str(e)}"
        )

@router.delete("/documents/{document_id}")
async def delete_document(
    document_id: int,
    current_user = Depends(get_current_user_adapter),
    session: AsyncSession = Depends(get_db_session)
):
    """Delete a document"""
    try:
        # Get document to verify ownership
        document = await DocumentServiceAdapter.get_document_by_id(document_id, session)
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        
        # Verify search space ownership
        search_space = await SearchSpaceServiceAdapter.get_search_space_by_id(
            document.search_space_id, str(current_user.id), session
        )
        if not search_space:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        success = await DocumentServiceAdapter.delete_document(document_id, session)
        
        if success:
            return {
                "message": f"Document deleted using {db_mode_config.mode} database",
                "document_id": document_id
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete document"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete document: {str(e)}"
        )

# Search Routes
@router.post("/search/documents")
async def search_documents(
    request: SearchRequest,
    current_user = Depends(get_current_user_adapter),
    session: AsyncSession = Depends(get_db_session)
):
    """Perform hybrid search on documents"""
    try:
        results = await SearchServiceAdapter.hybrid_search_documents(
            request.query, request.top_k, str(current_user.id), 
            request.search_space_id, request.document_type, session
        )
        
        return {
            "results": ResponseFormatAdapter.format_search_results(results),
            "query": request.query,
            "total_results": len(results),
            "database_mode": db_mode_config.mode,
            "search_type": "hybrid"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Search failed: {str(e)}"
        )

@router.post("/search/chunks")
async def search_chunks(
    request: SearchRequest,
    current_user = Depends(get_current_user_adapter),
    session: AsyncSession = Depends(get_db_session)
):
    """Perform hybrid search on chunks"""
    try:
        results = await SearchServiceAdapter.hybrid_search_chunks(
            request.query, request.top_k, str(current_user.id), 
            request.search_space_id, request.document_type, session
        )
        
        return {
            "results": ResponseFormatAdapter.format_search_results(results),
            "query": request.query,
            "total_results": len(results),
            "database_mode": db_mode_config.mode,
            "search_type": "hybrid"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Search failed: {str(e)}"
        )

@router.post("/search/vector-documents")
async def vector_search_documents(
    request: SearchRequest,
    current_user = Depends(get_current_user_adapter),
    session: AsyncSession = Depends(get_db_session)
):
    """Perform vector-only search on documents"""
    try:
        results = await SearchServiceAdapter.vector_search_documents(
            request.query, request.top_k, str(current_user.id), 
            request.search_space_id, session
        )
        
        return {
            "results": [
                ResponseFormatAdapter.format_document_response(doc) 
                for doc in results
            ],
            "query": request.query,
            "total_results": len(results),
            "database_mode": db_mode_config.mode,
            "search_type": "vector"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Vector search failed: {str(e)}"
        )

# System Info Routes
@router.get("/system/info")
async def get_system_info():
    """Get information about the current database system"""
    return {
        "database_mode": db_mode_config.mode,
        "local_config": {
            "faiss_index_type": db_mode_config.local_config.faiss_index_type,
            "batch_size": db_mode_config.local_config.batch_size,
            "max_memory_mb": db_mode_config.local_config.max_memory_usage_mb,
            "rrf_k": db_mode_config.local_config.rrf_k
        } if db_mode_config.is_local_mode() else None,
        "features": {
            "vector_search": True,
            "full_text_search": True,
            "hybrid_search": True,
            "auto_migration": db_mode_config.auto_migrate,
            "performance_monitoring": db_mode_config.local_config.enable_performance_logging
        }
    }

@router.get("/system/stats")
async def get_system_stats(
    search_space_id: Optional[int] = Query(None),
    current_user = Depends(get_current_user_adapter)
):
    """Get system performance statistics"""
    try:
        if db_mode_config.is_local_mode():
            from .service_adapters import local_db_service
            stats = await local_db_service.get_search_stats(search_space_id)
            
            from .config import performance_monitor
            perf_stats = performance_monitor.get_performance_summary()
            
            return {
                "database_mode": db_mode_config.mode,
                "search_index_stats": stats,
                "performance_stats": perf_stats
            }
        else:
            return {
                "database_mode": db_mode_config.mode,
                "message": "Performance stats only available in local mode"
            }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system stats: {str(e)}"
        )
