"""
FAISS Index Manager for high-performance vector similarity search.
Handles multiple indices for different search spaces and automatic persistence.
"""

import os
import pickle
import numpy as np
import faiss
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import asyncio
import threading
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class VectorRecord:
    """Represents a vector record with metadata"""
    id: int
    vector: np.ndarray
    search_space_id: int
    record_type: str  # 'document' or 'chunk'
    metadata: Dict[str, Any]

class FAISSIndexManager:
    """
    High-performance FAISS index manager with multi-index support.
    Optimized for SurfSense's search patterns.
    """
    
    def __init__(self, index_dir: str = "faiss_indices", embedding_dim: int = 1024):
        self.index_dir = Path(index_dir)
        self.index_dir.mkdir(exist_ok=True)
        self.embedding_dim = embedding_dim
        
        # In-memory indices for fast access
        self.document_indices: Dict[int, faiss.Index] = {}  # search_space_id -> index
        self.chunk_indices: Dict[int, faiss.Index] = {}
        
        # Metadata mappings: search_space_id -> {faiss_id -> record_metadata}
        self.document_metadata: Dict[int, Dict[int, Dict]] = {}
        self.chunk_metadata: Dict[int, Dict[int, Dict]] = {}
        
        # ID mappings: search_space_id -> {db_id -> faiss_id}
        self.document_id_mapping: Dict[int, Dict[int, int]] = {}
        self.chunk_id_mapping: Dict[int, Dict[int, int]] = {}
        
        # Reverse mappings: search_space_id -> {faiss_id -> db_id}
        self.document_reverse_mapping: Dict[int, Dict[int, int]] = {}
        self.chunk_reverse_mapping: Dict[int, Dict[int, int]] = {}
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Load existing indices
        self._load_all_indices()
    
    def _get_index_path(self, search_space_id: int, record_type: str) -> Path:
        """Get the file path for a specific index"""
        return self.index_dir / f"{record_type}_{search_space_id}.faiss"
    
    def _get_metadata_path(self, search_space_id: int, record_type: str) -> Path:
        """Get the file path for index metadata"""
        return self.index_dir / f"{record_type}_{search_space_id}_metadata.pkl"
    
    def _create_index(self, search_space_id: int, record_type: str) -> faiss.Index:
        """Create a new FAISS index optimized for the use case"""
        # Use HNSW for high-quality approximate search
        # This matches the performance characteristics of pgvector's HNSW
        index = faiss.IndexHNSWFlat(self.embedding_dim, 32)  # 32 connections per node
        index.hnsw.efConstruction = 200  # Higher quality construction
        index.hnsw.efSearch = 100  # Good search quality
        
        logger.info(f"Created new FAISS index for {record_type} in search_space {search_space_id}")
        return index
    
    def _load_all_indices(self):
        """Load all existing indices from disk"""
        with self._lock:
            for index_file in self.index_dir.glob("*.faiss"):
                try:
                    parts = index_file.stem.split('_')
                    if len(parts) >= 2:
                        record_type = parts[0]
                        search_space_id = int(parts[1])
                        
                        # Load index
                        index = faiss.read_index(str(index_file))
                        
                        # Load metadata
                        metadata_file = self._get_metadata_path(search_space_id, record_type)
                        if metadata_file.exists():
                            with open(metadata_file, 'rb') as f:
                                metadata_dict = pickle.load(f)
                                
                            if record_type == 'document':
                                self.document_indices[search_space_id] = index
                                self.document_metadata[search_space_id] = metadata_dict['metadata']
                                self.document_id_mapping[search_space_id] = metadata_dict['id_mapping']
                                self.document_reverse_mapping[search_space_id] = metadata_dict['reverse_mapping']
                            elif record_type == 'chunk':
                                self.chunk_indices[search_space_id] = index
                                self.chunk_metadata[search_space_id] = metadata_dict['metadata']
                                self.chunk_id_mapping[search_space_id] = metadata_dict['id_mapping']
                                self.chunk_reverse_mapping[search_space_id] = metadata_dict['reverse_mapping']
                                
                        logger.info(f"Loaded {record_type} index for search_space {search_space_id}")
                        
                except Exception as e:
                    logger.error(f"Failed to load index {index_file}: {e}")
    
    def _save_index(self, search_space_id: int, record_type: str):
        """Save index and metadata to disk"""
        try:
            if record_type == 'document':
                index = self.document_indices.get(search_space_id)
                metadata = self.document_metadata.get(search_space_id, {})
                id_mapping = self.document_id_mapping.get(search_space_id, {})
                reverse_mapping = self.document_reverse_mapping.get(search_space_id, {})
            else:
                index = self.chunk_indices.get(search_space_id)
                metadata = self.chunk_metadata.get(search_space_id, {})
                id_mapping = self.chunk_id_mapping.get(search_space_id, {})
                reverse_mapping = self.chunk_reverse_mapping.get(search_space_id, {})
            
            if index is not None:
                # Save FAISS index
                index_path = self._get_index_path(search_space_id, record_type)
                faiss.write_index(index, str(index_path))
                
                # Save metadata
                metadata_path = self._get_metadata_path(search_space_id, record_type)
                metadata_dict = {
                    'metadata': metadata,
                    'id_mapping': id_mapping,
                    'reverse_mapping': reverse_mapping,
                    'saved_at': datetime.now().isoformat()
                }
                with open(metadata_path, 'wb') as f:
                    pickle.dump(metadata_dict, f)
                    
                logger.info(f"Saved {record_type} index for search_space {search_space_id}")
                
        except Exception as e:
            logger.error(f"Failed to save {record_type} index for search_space {search_space_id}: {e}")
    
    async def add_vectors(self, records: List[VectorRecord]) -> bool:
        """Add multiple vectors to the appropriate indices"""
        def _add_vectors_sync():
            with self._lock:
                try:
                    # Group records by search_space_id and type
                    grouped_records = {}
                    for record in records:
                        key = (record.search_space_id, record.record_type)
                        if key not in grouped_records:
                            grouped_records[key] = []
                        grouped_records[key].append(record)
                    
                    # Process each group
                    for (search_space_id, record_type), group_records in grouped_records.items():
                        # Get or create index
                        if record_type == 'document':
                            if search_space_id not in self.document_indices:
                                self.document_indices[search_space_id] = self._create_index(search_space_id, record_type)
                                self.document_metadata[search_space_id] = {}
                                self.document_id_mapping[search_space_id] = {}
                                self.document_reverse_mapping[search_space_id] = {}
                            
                            index = self.document_indices[search_space_id]
                            metadata_dict = self.document_metadata[search_space_id]
                            id_mapping = self.document_id_mapping[search_space_id]
                            reverse_mapping = self.document_reverse_mapping[search_space_id]
                        else:
                            if search_space_id not in self.chunk_indices:
                                self.chunk_indices[search_space_id] = self._create_index(search_space_id, record_type)
                                self.chunk_metadata[search_space_id] = {}
                                self.chunk_id_mapping[search_space_id] = {}
                                self.chunk_reverse_mapping[search_space_id] = {}
                            
                            index = self.chunk_indices[search_space_id]
                            metadata_dict = self.chunk_metadata[search_space_id]
                            id_mapping = self.chunk_id_mapping[search_space_id]
                            reverse_mapping = self.chunk_reverse_mapping[search_space_id]
                        
                        # Prepare vectors and metadata
                        vectors = np.array([record.vector for record in group_records], dtype=np.float32)
                        
                        # Add to FAISS index
                        start_faiss_id = index.ntotal
                        index.add(vectors)
                        
                        # Update mappings and metadata
                        for i, record in enumerate(group_records):
                            faiss_id = start_faiss_id + i
                            id_mapping[record.id] = faiss_id
                            reverse_mapping[faiss_id] = record.id
                            metadata_dict[faiss_id] = record.metadata
                        
                        # Save to disk
                        self._save_index(search_space_id, record_type)
                    
                    return True
                    
                except Exception as e:
                    logger.error(f"Failed to add vectors: {e}")
                    return False
        
        # Run in thread pool to avoid blocking
        return await asyncio.get_event_loop().run_in_executor(None, _add_vectors_sync)

    async def search_vectors(self, query_vector: np.ndarray, search_space_id: int,
                           record_type: str, top_k: int = 10) -> List[Tuple[int, float, Dict]]:
        """
        Search for similar vectors in the specified index.

        Returns:
            List of (db_id, similarity_score, metadata) tuples
        """
        def _search_sync():
            with self._lock:
                try:
                    if record_type == 'document':
                        index = self.document_indices.get(search_space_id)
                        metadata_dict = self.document_metadata.get(search_space_id, {})
                        reverse_mapping = self.document_reverse_mapping.get(search_space_id, {})
                    else:
                        index = self.chunk_indices.get(search_space_id)
                        metadata_dict = self.chunk_metadata.get(search_space_id, {})
                        reverse_mapping = self.chunk_reverse_mapping.get(search_space_id, {})

                    if index is None or index.ntotal == 0:
                        return []

                    # Perform search
                    query_vector = query_vector.reshape(1, -1).astype(np.float32)
                    distances, indices = index.search(query_vector, min(top_k, index.ntotal))

                    # Convert results
                    results = []
                    for i, (distance, faiss_id) in enumerate(zip(distances[0], indices[0])):
                        if faiss_id == -1:  # FAISS returns -1 for invalid results
                            continue

                        db_id = reverse_mapping.get(faiss_id)
                        if db_id is not None:
                            # Convert distance to similarity score (higher is better)
                            similarity_score = 1.0 / (1.0 + distance)
                            metadata = metadata_dict.get(faiss_id, {})
                            results.append((db_id, similarity_score, metadata))

                    return results

                except Exception as e:
                    logger.error(f"Failed to search vectors: {e}")
                    return []

        return await asyncio.get_event_loop().run_in_executor(None, _search_sync)

    async def remove_vectors(self, db_ids: List[int], search_space_id: int, record_type: str) -> bool:
        """Remove vectors by their database IDs"""
        def _remove_sync():
            with self._lock:
                try:
                    if record_type == 'document':
                        index = self.document_indices.get(search_space_id)
                        metadata_dict = self.document_metadata.get(search_space_id, {})
                        id_mapping = self.document_id_mapping.get(search_space_id, {})
                        reverse_mapping = self.document_reverse_mapping.get(search_space_id, {})
                    else:
                        index = self.chunk_indices.get(search_space_id)
                        metadata_dict = self.chunk_metadata.get(search_space_id, {})
                        id_mapping = self.chunk_id_mapping.get(search_space_id, {})
                        reverse_mapping = self.chunk_reverse_mapping.get(search_space_id, {})

                    if index is None:
                        return True

                    # FAISS doesn't support direct removal, so we need to rebuild the index
                    # For now, we'll mark as removed in metadata and rebuild periodically
                    removed_faiss_ids = []
                    for db_id in db_ids:
                        faiss_id = id_mapping.get(db_id)
                        if faiss_id is not None:
                            removed_faiss_ids.append(faiss_id)
                            # Clean up mappings
                            del id_mapping[db_id]
                            del reverse_mapping[faiss_id]
                            if faiss_id in metadata_dict:
                                del metadata_dict[faiss_id]

                    # If we removed many vectors, consider rebuilding the index
                    if len(removed_faiss_ids) > index.ntotal * 0.1:  # 10% threshold
                        await self._rebuild_index(search_space_id, record_type)
                    else:
                        self._save_index(search_space_id, record_type)

                    return True

                except Exception as e:
                    logger.error(f"Failed to remove vectors: {e}")
                    return False

        return await asyncio.get_event_loop().run_in_executor(None, _remove_sync)

    async def _rebuild_index(self, search_space_id: int, record_type: str):
        """Rebuild an index to remove deleted vectors"""
        # This would require fetching all valid vectors from the database
        # and recreating the index - implement based on your needs
        logger.info(f"Index rebuild needed for {record_type} in search_space {search_space_id}")
        pass

    async def get_index_stats(self, search_space_id: int = None) -> Dict[str, Any]:
        """Get statistics about the indices"""
        with self._lock:
            stats = {
                'total_document_indices': len(self.document_indices),
                'total_chunk_indices': len(self.chunk_indices),
                'search_spaces': {}
            }

            if search_space_id is not None:
                # Stats for specific search space
                doc_index = self.document_indices.get(search_space_id)
                chunk_index = self.chunk_indices.get(search_space_id)

                stats['search_spaces'][search_space_id] = {
                    'document_vectors': doc_index.ntotal if doc_index else 0,
                    'chunk_vectors': chunk_index.ntotal if chunk_index else 0
                }
            else:
                # Stats for all search spaces
                all_spaces = set(self.document_indices.keys()) | set(self.chunk_indices.keys())
                for space_id in all_spaces:
                    doc_index = self.document_indices.get(space_id)
                    chunk_index = self.chunk_indices.get(space_id)

                    stats['search_spaces'][space_id] = {
                        'document_vectors': doc_index.ntotal if doc_index else 0,
                        'chunk_vectors': chunk_index.ntotal if chunk_index else 0
                    }

            return stats

    def cleanup(self):
        """Clean up resources"""
        with self._lock:
            # Save all indices before cleanup
            for search_space_id in self.document_indices:
                self._save_index(search_space_id, 'document')
            for search_space_id in self.chunk_indices:
                self._save_index(search_space_id, 'chunk')

            logger.info("FAISS Index Manager cleanup completed")
