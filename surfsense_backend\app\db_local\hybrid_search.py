"""
Local Hybrid Search Service combining FAISS vector search with SQLite FTS5.
Provides the same interface as the PostgreSQL-based search but with superior performance.
"""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, func
from sqlalchemy.orm import joinedload
import asyncio
import logging

from .faiss_manager import FAISSIndexManager, VectorRecord
from .sqlite_models import LocalDocument, LocalChunk, LocalSearchSpace
from app.config import config

logger = logging.getLogger(__name__)

class LocalHybridSearchService:
    """
    High-performance hybrid search service using FAISS + SQLite FTS5.
    Replaces PostgreSQL pgvector with superior local performance.
    """
    
    def __init__(self, faiss_manager: FAISSIndexManager):
        self.faiss_manager = faiss_manager
        self.embedding_model = config.embedding_model_instance
    
    async def add_document(self, session: AsyncSession, document: LocalDocument, 
                          embedding: Optional[np.ndarray] = None) -> bool:
        """Add a document to both SQLite and FAISS indices"""
        try:
            # Generate embedding if not provided
            if embedding is None:
                embedding = await asyncio.to_thread(
                    self.embedding_model.embed, document.content
                )
            
            # Create vector record
            vector_record = VectorRecord(
                id=document.id,
                vector=embedding,
                search_space_id=document.search_space_id,
                record_type='document',
                metadata={
                    'title': document.title,
                    'document_type': document.document_type,
                    'content_hash': document.content_hash,
                    'created_at': document.created_at.isoformat() if document.created_at else None
                }
            )
            
            # Add to FAISS index
            success = await self.faiss_manager.add_vectors([vector_record])
            if success:
                logger.info(f"Added document {document.id} to FAISS index")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to add document {document.id} to search index: {e}")
            return False
    
    async def add_chunk(self, session: AsyncSession, chunk: LocalChunk, 
                       embedding: Optional[np.ndarray] = None) -> bool:
        """Add a chunk to both SQLite and FAISS indices"""
        try:
            # Generate embedding if not provided
            if embedding is None:
                embedding = await asyncio.to_thread(
                    self.embedding_model.embed, chunk.content
                )
            
            # Get document info for metadata
            result = await session.execute(
                select(LocalDocument.search_space_id, LocalDocument.title)
                .where(LocalDocument.id == chunk.document_id)
            )
            doc_info = result.first()
            
            if not doc_info:
                logger.error(f"Document {chunk.document_id} not found for chunk {chunk.id}")
                return False
            
            # Create vector record
            vector_record = VectorRecord(
                id=chunk.id,
                vector=embedding,
                search_space_id=doc_info.search_space_id,
                record_type='chunk',
                metadata={
                    'document_id': chunk.document_id,
                    'document_title': doc_info.title,
                    'created_at': chunk.created_at.isoformat() if chunk.created_at else None
                }
            )
            
            # Add to FAISS index
            success = await self.faiss_manager.add_vectors([vector_record])
            if success:
                logger.info(f"Added chunk {chunk.id} to FAISS index")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to add chunk {chunk.id} to search index: {e}")
            return False
    
    async def vector_search_documents(self, query_text: str, top_k: int, user_id: str, 
                                    search_space_id: Optional[int] = None) -> List[LocalDocument]:
        """Perform vector similarity search on documents"""
        try:
            # Generate query embedding
            query_embedding = await asyncio.to_thread(
                self.embedding_model.embed, query_text
            )
            
            # Search in FAISS
            if search_space_id:
                search_results = await self.faiss_manager.search_vectors(
                    query_embedding, search_space_id, 'document', top_k
                )
            else:
                # Search across all user's search spaces
                # This requires getting all search spaces for the user first
                search_results = []
                # Implementation would need to iterate through user's search spaces
            
            if not search_results:
                return []
            
            # Get document IDs from search results
            doc_ids = [result[0] for result in search_results]
            
            # Fetch documents from SQLite with proper ordering
            async with AsyncSession(bind=session.bind) as session:
                result = await session.execute(
                    select(LocalDocument)
                    .options(joinedload(LocalDocument.search_space))
                    .join(LocalSearchSpace)
                    .where(
                        LocalDocument.id.in_(doc_ids),
                        LocalSearchSpace.user_id == user_id
                    )
                )
                documents = result.scalars().all()
            
            # Maintain FAISS ordering
            doc_dict = {doc.id: doc for doc in documents}
            ordered_documents = [doc_dict[doc_id] for doc_id in doc_ids if doc_id in doc_dict]
            
            return ordered_documents
            
        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            return []
    
    async def vector_search_chunks(self, query_text: str, top_k: int, user_id: str, 
                                 search_space_id: Optional[int] = None) -> List[LocalChunk]:
        """Perform vector similarity search on chunks"""
        try:
            # Generate query embedding
            query_embedding = await asyncio.to_thread(
                self.embedding_model.embed, query_text
            )
            
            # Search in FAISS
            if search_space_id:
                search_results = await self.faiss_manager.search_vectors(
                    query_embedding, search_space_id, 'chunk', top_k
                )
            else:
                # Search across all user's search spaces
                search_results = []
                # Implementation would need to iterate through user's search spaces
            
            if not search_results:
                return []
            
            # Get chunk IDs from search results
            chunk_ids = [result[0] for result in search_results]
            
            # Fetch chunks from SQLite with proper ordering
            async with AsyncSession(bind=session.bind) as session:
                result = await session.execute(
                    select(LocalChunk)
                    .options(joinedload(LocalChunk.document).joinedload(LocalDocument.search_space))
                    .join(LocalDocument)
                    .join(LocalSearchSpace)
                    .where(
                        LocalChunk.id.in_(chunk_ids),
                        LocalSearchSpace.user_id == user_id
                    )
                )
                chunks = result.scalars().all()
            
            # Maintain FAISS ordering
            chunk_dict = {chunk.id: chunk for chunk in chunks}
            ordered_chunks = [chunk_dict[chunk_id] for chunk_id in chunk_ids if chunk_id in chunk_dict]
            
            return ordered_chunks
            
        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            return []
    
    async def full_text_search_documents(self, session: AsyncSession, query_text: str, 
                                       top_k: int, user_id: str, 
                                       search_space_id: Optional[int] = None) -> List[LocalDocument]:
        """Perform full-text search on documents using SQLite FTS5"""
        try:
            # Build FTS5 query
            fts_query = self._build_fts_query(query_text)
            
            # Base query with user ownership check
            base_query = """
                SELECT d.*, rank
                FROM documents_fts fts
                JOIN documents d ON d.id = fts.id
                JOIN search_spaces ss ON d.search_space_id = ss.id
                WHERE fts MATCH ? AND ss.user_id = ?
            """
            
            params = [fts_query, user_id]
            
            # Add search space filter if provided
            if search_space_id is not None:
                base_query += " AND d.search_space_id = ?"
                params.append(search_space_id)
            
            # Order by FTS5 rank and limit
            base_query += " ORDER BY rank LIMIT ?"
            params.append(top_k)
            
            # Execute raw SQL query
            result = await session.execute(text(base_query), params)
            rows = result.fetchall()
            
            if not rows:
                return []
            
            # Convert to LocalDocument objects
            doc_ids = [row[0] for row in rows]  # Assuming id is first column
            
            # Fetch full document objects
            result = await session.execute(
                select(LocalDocument)
                .options(joinedload(LocalDocument.search_space))
                .where(LocalDocument.id.in_(doc_ids))
            )
            documents = result.scalars().all()
            
            # Maintain FTS ordering
            doc_dict = {doc.id: doc for doc in documents}
            ordered_documents = [doc_dict[doc_id] for doc_id in doc_ids if doc_id in doc_dict]
            
            return ordered_documents
            
        except Exception as e:
            logger.error(f"Full-text search failed: {e}")
            return []
    
    async def full_text_search_chunks(self, session: AsyncSession, query_text: str, 
                                    top_k: int, user_id: str, 
                                    search_space_id: Optional[int] = None) -> List[LocalChunk]:
        """Perform full-text search on chunks using SQLite FTS5"""
        try:
            # Build FTS5 query
            fts_query = self._build_fts_query(query_text)
            
            # Base query with user ownership check
            base_query = """
                SELECT c.*, rank
                FROM chunks_fts fts
                JOIN chunks c ON c.id = fts.id
                JOIN documents d ON c.document_id = d.id
                JOIN search_spaces ss ON d.search_space_id = ss.id
                WHERE fts MATCH ? AND ss.user_id = ?
            """
            
            params = [fts_query, user_id]
            
            # Add search space filter if provided
            if search_space_id is not None:
                base_query += " AND d.search_space_id = ?"
                params.append(search_space_id)
            
            # Order by FTS5 rank and limit
            base_query += " ORDER BY rank LIMIT ?"
            params.append(top_k)
            
            # Execute raw SQL query
            result = await session.execute(text(base_query), params)
            rows = result.fetchall()
            
            if not rows:
                return []
            
            # Convert to LocalChunk objects
            chunk_ids = [row[0] for row in rows]  # Assuming id is first column
            
            # Fetch full chunk objects
            result = await session.execute(
                select(LocalChunk)
                .options(joinedload(LocalChunk.document).joinedload(LocalDocument.search_space))
                .where(LocalChunk.id.in_(chunk_ids))
            )
            chunks = result.scalars().all()
            
            # Maintain FTS ordering
            chunk_dict = {chunk.id: chunk for chunk in chunks}
            ordered_chunks = [chunk_dict[chunk_id] for chunk_id in chunk_ids if chunk_id in chunk_dict]
            
            return ordered_chunks
            
        except Exception as e:
            logger.error(f"Full-text search failed: {e}")
            return []
    
    def _build_fts_query(self, query_text: str) -> str:
        """Build FTS5 query from user input"""
        # Simple implementation - can be enhanced with phrase detection, etc.
        # FTS5 uses different syntax than PostgreSQL's tsquery
        words = query_text.strip().split()
        if len(words) == 1:
            return words[0]
        else:
            # Use AND for multiple words
            return " AND ".join(words)
    
    async def hybrid_search_documents(self, session: AsyncSession, query_text: str, 
                                    top_k: int, user_id: str, 
                                    search_space_id: Optional[int] = None,
                                    document_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Perform hybrid search combining vector similarity and full-text search.
        Uses Reciprocal Rank Fusion (RRF) to combine results.
        """
        try:
            # Get more results for better fusion
            n_results = top_k * 2
            
            # Perform both searches concurrently
            vector_task = self.vector_search_documents(query_text, n_results, user_id, search_space_id)
            fts_task = self.full_text_search_documents(session, query_text, n_results, user_id, search_space_id)
            
            vector_results, fts_results = await asyncio.gather(vector_task, fts_task)
            
            # Apply document type filter if specified
            if document_type:
                vector_results = [doc for doc in vector_results if doc.document_type == document_type]
                fts_results = [doc for doc in fts_results if doc.document_type == document_type]
            
            # Combine using RRF
            combined_results = self._reciprocal_rank_fusion(
                vector_results, fts_results, top_k
            )
            
            return combined_results

        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return []

    async def hybrid_search_chunks(self, session: AsyncSession, query_text: str,
                                 top_k: int, user_id: str,
                                 search_space_id: Optional[int] = None,
                                 document_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Perform hybrid search on chunks combining vector similarity and full-text search.
        """
        try:
            # Get more results for better fusion
            n_results = top_k * 2

            # Perform both searches concurrently
            vector_task = self.vector_search_chunks(query_text, n_results, user_id, search_space_id)
            fts_task = self.full_text_search_chunks(session, query_text, n_results, user_id, search_space_id)

            vector_results, fts_results = await asyncio.gather(vector_task, fts_task)

            # Apply document type filter if specified
            if document_type:
                vector_results = [chunk for chunk in vector_results
                                if chunk.document.document_type == document_type]
                fts_results = [chunk for chunk in fts_results
                             if chunk.document.document_type == document_type]

            # Combine using RRF
            combined_results = self._reciprocal_rank_fusion(
                vector_results, fts_results, top_k
            )

            return combined_results

        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return []

    def _reciprocal_rank_fusion(self, vector_results: List[Any], fts_results: List[Any],
                               top_k: int, k: int = 60) -> List[Dict[str, Any]]:
        """
        Combine search results using Reciprocal Rank Fusion.

        Args:
            vector_results: Results from vector search (ordered by similarity)
            fts_results: Results from full-text search (ordered by relevance)
            top_k: Number of final results to return
            k: RRF constant (default 60)

        Returns:
            List of combined results with scores
        """
        # Create score dictionaries
        vector_scores = {}
        fts_scores = {}

        # Calculate RRF scores for vector results
        for rank, item in enumerate(vector_results, 1):
            item_id = item.id
            vector_scores[item_id] = 1.0 / (k + rank)

        # Calculate RRF scores for FTS results
        for rank, item in enumerate(fts_results, 1):
            item_id = item.id
            fts_scores[item_id] = 1.0 / (k + rank)

        # Combine scores
        all_items = {}
        all_item_ids = set(vector_scores.keys()) | set(fts_scores.keys())

        for item_id in all_item_ids:
            # Find the item object
            item = None
            for result in vector_results:
                if result.id == item_id:
                    item = result
                    break
            if item is None:
                for result in fts_results:
                    if result.id == item_id:
                        item = result
                        break

            if item is not None:
                combined_score = vector_scores.get(item_id, 0.0) + fts_scores.get(item_id, 0.0)
                all_items[item_id] = {
                    'item': item,
                    'score': combined_score,
                    'vector_score': vector_scores.get(item_id, 0.0),
                    'fts_score': fts_scores.get(item_id, 0.0)
                }

        # Sort by combined score and return top_k
        sorted_items = sorted(all_items.values(), key=lambda x: x['score'], reverse=True)
        return sorted_items[:top_k]

    async def remove_document(self, document_id: int, search_space_id: int) -> bool:
        """Remove a document from the search indices"""
        try:
            success = await self.faiss_manager.remove_vectors(
                [document_id], search_space_id, 'document'
            )
            if success:
                logger.info(f"Removed document {document_id} from search index")
            return success
        except Exception as e:
            logger.error(f"Failed to remove document {document_id}: {e}")
            return False

    async def remove_chunk(self, chunk_id: int, search_space_id: int) -> bool:
        """Remove a chunk from the search indices"""
        try:
            success = await self.faiss_manager.remove_vectors(
                [chunk_id], search_space_id, 'chunk'
            )
            if success:
                logger.info(f"Removed chunk {chunk_id} from search index")
            return success
        except Exception as e:
            logger.error(f"Failed to remove chunk {chunk_id}: {e}")
            return False

    async def get_search_stats(self, search_space_id: Optional[int] = None) -> Dict[str, Any]:
        """Get search index statistics"""
        return await self.faiss_manager.get_index_stats(search_space_id)
