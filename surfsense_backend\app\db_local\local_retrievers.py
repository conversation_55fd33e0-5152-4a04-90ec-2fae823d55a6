"""
Local retrievers that provide the same interface as the PostgreSQL retrievers
but use FAISS + SQLite for superior performance.
"""

from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from .service_adapters import local_db_service
from .sqlite_models import LocalDocument, LocalChunk

logger = logging.getLogger(__name__)

class LocalDocumentHybridSearchRetriever:
    """
    Local document retriever that mirrors the interface of DocumentHybridSearchRetriever
    but uses FAISS + SQLite for superior performance.
    """
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
    
    async def vector_search(self, query_text: str, top_k: int, user_id: str, 
                          search_space_id: Optional[int] = None) -> List[LocalDocument]:
        """
        Perform vector similarity search on documents.
        
        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            
        Returns:
            List of documents sorted by vector similarity
        """
        try:
            return await local_db_service.vector_search_documents(
                query_text, top_k, user_id, search_space_id
            )
        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            return []
    
    async def full_text_search(self, query_text: str, top_k: int, user_id: str, 
                             search_space_id: Optional[int] = None) -> List[LocalDocument]:
        """
        Perform full-text keyword search on documents.
        
        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            
        Returns:
            List of documents sorted by text relevance
        """
        try:
            return await local_db_service.search_service.full_text_search_documents(
                self.db_session, query_text, top_k, user_id, search_space_id
            )
        except Exception as e:
            logger.error(f"Full-text search failed: {e}")
            return []
    
    async def hybrid_search(self, query_text: str, top_k: int, user_id: str, 
                          search_space_id: Optional[int] = None, 
                          document_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Combine vector similarity and full-text search results using Reciprocal Rank Fusion.
        
        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            document_type: Optional document type to filter results
            
        Returns:
            List of dictionaries containing document data and relevance scores
        """
        try:
            results = await local_db_service.hybrid_search_documents(
                self.db_session, query_text, top_k, user_id, search_space_id, document_type
            )
            
            # Convert to format expected by existing code
            formatted_results = []
            for result in results:
                document = result['item']
                formatted_results.append({
                    'document': document,
                    'score': result['score'],
                    'vector_score': result['vector_score'],
                    'fts_score': result['fts_score'],
                    # Add fields that existing code might expect
                    'id': document.id,
                    'title': document.title,
                    'content': document.content,
                    'document_type': document.document_type,
                    'search_space_id': document.search_space_id,
                    'created_at': document.created_at
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return []

class LocalChunkHybridSearchRetriever:
    """
    Local chunk retriever that mirrors the interface of ChucksHybridSearchRetriever
    but uses FAISS + SQLite for superior performance.
    """
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
    
    async def vector_search(self, query_text: str, top_k: int, user_id: str, 
                          search_space_id: Optional[int] = None) -> List[LocalChunk]:
        """
        Perform vector similarity search on chunks.
        
        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            
        Returns:
            List of chunks sorted by vector similarity
        """
        try:
            return await local_db_service.vector_search_chunks(
                query_text, top_k, user_id, search_space_id
            )
        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            return []
    
    async def full_text_search(self, query_text: str, top_k: int, user_id: str, 
                             search_space_id: Optional[int] = None) -> List[LocalChunk]:
        """
        Perform full-text keyword search on chunks.
        
        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            
        Returns:
            List of chunks sorted by text relevance
        """
        try:
            return await local_db_service.search_service.full_text_search_chunks(
                self.db_session, query_text, top_k, user_id, search_space_id
            )
        except Exception as e:
            logger.error(f"Full-text search failed: {e}")
            return []
    
    async def hybrid_search(self, query_text: str, top_k: int, user_id: str, 
                          search_space_id: Optional[int] = None, 
                          document_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Combine vector similarity and full-text search results using Reciprocal Rank Fusion.
        
        Args:
            query_text: The search query text
            top_k: Number of results to return
            user_id: The ID of the user performing the search
            search_space_id: Optional search space ID to filter results
            document_type: Optional document type to filter results
            
        Returns:
            List of dictionaries containing chunk data and relevance scores
        """
        try:
            results = await local_db_service.hybrid_search_chunks(
                self.db_session, query_text, top_k, user_id, search_space_id, document_type
            )
            
            # Convert to format expected by existing code
            formatted_results = []
            for result in results:
                chunk = result['item']
                formatted_results.append({
                    'chunk': chunk,
                    'score': result['score'],
                    'vector_score': result['vector_score'],
                    'fts_score': result['fts_score'],
                    # Add fields that existing code might expect
                    'id': chunk.id,
                    'content': chunk.content,
                    'document_id': chunk.document_id,
                    'document': chunk.document,
                    'created_at': chunk.created_at
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return []

# Factory functions to create retrievers with the same interface as the original
def create_local_document_retriever(db_session: AsyncSession) -> LocalDocumentHybridSearchRetriever:
    """Create a local document retriever"""
    return LocalDocumentHybridSearchRetriever(db_session)

def create_local_chunk_retriever(db_session: AsyncSession) -> LocalChunkHybridSearchRetriever:
    """Create a local chunk retriever"""
    return LocalChunkHybridSearchRetriever(db_session)

# Compatibility layer - these can be used as drop-in replacements
class DocumentHybridSearchRetriever(LocalDocumentHybridSearchRetriever):
    """Compatibility alias for existing code"""
    pass

class ChucksHybridSearchRetriever(LocalChunkHybridSearchRetriever):
    """Compatibility alias for existing code"""
    pass
