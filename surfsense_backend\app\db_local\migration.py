"""
Database migration system to transfer data from PostgreSQL to FAISS + SQLite.
Handles data validation, embedding migration, and rollback capabilities.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import json
import numpy as np
from pathlib import Path

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import select, text, func
from sqlalchemy.orm import joinedload

# PostgreSQL imports
from app.db import (
    User as PgUser, SearchSpace as PgSearchSpace, Document as PgDocument, 
    Chunk as PgChunk, Chat as PgChat, Podcast as PgPodcast, 
    LLMConfig as PgLLMConfig, SearchSourceConnector as PgSearchSourceConnector,
    get_async_session as get_pg_session, DATABASE_URL as PG_DATABASE_URL
)

# Local database imports
from .sqlite_models import (
    LocalUser, LocalSearchSpace, LocalDocument, LocalChunk, 
    LocalChat, LocalPodcast, LocalLLMConfig, LocalSearchSourceConnector,
    init_local_database
)
from .service_adapters import local_db_service
from .faiss_manager import VectorRecord
from app.config import config

logger = logging.getLogger(__name__)

class DatabaseMigrator:
    """
    Comprehensive database migration system from PostgreSQL to FAISS + SQLite.
    """
    
    def __init__(self, batch_size: int = 100, backup_dir: str = "migration_backup"):
        self.batch_size = batch_size
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
        
        # Statistics
        self.migration_stats = {
            'users': {'total': 0, 'migrated': 0, 'failed': 0},
            'search_spaces': {'total': 0, 'migrated': 0, 'failed': 0},
            'documents': {'total': 0, 'migrated': 0, 'failed': 0},
            'chunks': {'total': 0, 'migrated': 0, 'failed': 0},
            'chats': {'total': 0, 'migrated': 0, 'failed': 0},
            'podcasts': {'total': 0, 'migrated': 0, 'failed': 0},
            'llm_configs': {'total': 0, 'migrated': 0, 'failed': 0},
            'connectors': {'total': 0, 'migrated': 0, 'failed': 0},
            'embeddings': {'total': 0, 'migrated': 0, 'failed': 0}
        }
        
        self.start_time = None
        self.end_time = None
    
    async def full_migration(self, validate_data: bool = True, 
                           create_backup: bool = True) -> Dict[str, Any]:
        """
        Perform complete migration from PostgreSQL to local system.
        
        Args:
            validate_data: Whether to validate migrated data
            create_backup: Whether to create backup before migration
            
        Returns:
            Migration report with statistics and any errors
        """
        self.start_time = datetime.now()
        logger.info("Starting full database migration from PostgreSQL to FAISS + SQLite")
        
        try:
            # Initialize local database
            await local_db_service.initialize()
            
            # Create backup if requested
            if create_backup:
                await self._create_backup()
            
            # Get PostgreSQL session
            pg_engine = create_async_engine(PG_DATABASE_URL)
            pg_session_maker = async_sessionmaker(pg_engine, expire_on_commit=False)
            
            async with pg_session_maker() as pg_session:
                async with local_db_service.get_session() as local_session:
                    # Migrate in order (respecting foreign key dependencies)
                    await self._migrate_users(pg_session, local_session)
                    await self._migrate_llm_configs(pg_session, local_session)
                    await self._migrate_search_source_connectors(pg_session, local_session)
                    await self._migrate_search_spaces(pg_session, local_session)
                    await self._migrate_documents(pg_session, local_session)
                    await self._migrate_chunks(pg_session, local_session)
                    await self._migrate_chats(pg_session, local_session)
                    await self._migrate_podcasts(pg_session, local_session)
            
            # Validate migration if requested
            if validate_data:
                validation_report = await self._validate_migration(pg_session_maker)
                self.migration_stats['validation'] = validation_report
            
            self.end_time = datetime.now()
            duration = (self.end_time - self.start_time).total_seconds()
            
            logger.info(f"Migration completed in {duration:.2f} seconds")
            
            return {
                'success': True,
                'duration_seconds': duration,
                'statistics': self.migration_stats,
                'start_time': self.start_time.isoformat(),
                'end_time': self.end_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'statistics': self.migration_stats,
                'start_time': self.start_time.isoformat() if self.start_time else None
            }
    
    async def _migrate_users(self, pg_session: AsyncSession, local_session: AsyncSession):
        """Migrate users from PostgreSQL to SQLite"""
        logger.info("Migrating users...")
        
        # Count total users
        result = await pg_session.execute(select(func.count(PgUser.id)))
        total_users = result.scalar()
        self.migration_stats['users']['total'] = total_users
        
        # Migrate in batches
        offset = 0
        while offset < total_users:
            result = await pg_session.execute(
                select(PgUser)
                .offset(offset)
                .limit(self.batch_size)
            )
            pg_users = result.scalars().all()
            
            for pg_user in pg_users:
                try:
                    local_user = LocalUser(
                        id=str(pg_user.id),
                        email=pg_user.email,
                        hashed_password=pg_user.hashed_password,
                        is_active=pg_user.is_active,
                        is_superuser=pg_user.is_superuser,
                        is_verified=pg_user.is_verified,
                        created_at=pg_user.created_at,
                        updated_at=pg_user.updated_at
                    )
                    
                    local_session.add(local_user)
                    self.migration_stats['users']['migrated'] += 1
                    
                except Exception as e:
                    logger.error(f"Failed to migrate user {pg_user.id}: {e}")
                    self.migration_stats['users']['failed'] += 1
            
            await local_session.commit()
            offset += self.batch_size
            logger.info(f"Migrated {min(offset, total_users)}/{total_users} users")
    
    async def _migrate_search_spaces(self, pg_session: AsyncSession, local_session: AsyncSession):
        """Migrate search spaces"""
        logger.info("Migrating search spaces...")
        
        result = await pg_session.execute(select(func.count(PgSearchSpace.id)))
        total_spaces = result.scalar()
        self.migration_stats['search_spaces']['total'] = total_spaces
        
        offset = 0
        while offset < total_spaces:
            result = await pg_session.execute(
                select(PgSearchSpace)
                .offset(offset)
                .limit(self.batch_size)
            )
            pg_spaces = result.scalars().all()
            
            for pg_space in pg_spaces:
                try:
                    local_space = LocalSearchSpace(
                        id=pg_space.id,
                        name=pg_space.name,
                        description=pg_space.description,
                        user_id=str(pg_space.user_id),
                        created_at=pg_space.created_at,
                        updated_at=pg_space.updated_at
                    )
                    
                    local_session.add(local_space)
                    self.migration_stats['search_spaces']['migrated'] += 1
                    
                except Exception as e:
                    logger.error(f"Failed to migrate search space {pg_space.id}: {e}")
                    self.migration_stats['search_spaces']['failed'] += 1
            
            await local_session.commit()
            offset += self.batch_size
            logger.info(f"Migrated {min(offset, total_spaces)}/{total_spaces} search spaces")
    
    async def _migrate_documents(self, pg_session: AsyncSession, local_session: AsyncSession):
        """Migrate documents and their embeddings"""
        logger.info("Migrating documents...")
        
        result = await pg_session.execute(select(func.count(PgDocument.id)))
        total_docs = result.scalar()
        self.migration_stats['documents']['total'] = total_docs
        
        # Collect vector records for batch FAISS insertion
        vector_records = []
        
        offset = 0
        while offset < total_docs:
            result = await pg_session.execute(
                select(PgDocument)
                .offset(offset)
                .limit(self.batch_size)
            )
            pg_docs = result.scalars().all()
            
            for pg_doc in pg_docs:
                try:
                    # Convert metadata
                    metadata_json = None
                    if pg_doc.document_metadata:
                        metadata_json = json.dumps(pg_doc.document_metadata)
                    
                    local_doc = LocalDocument(
                        id=pg_doc.id,
                        title=pg_doc.title,
                        document_type=pg_doc.document_type.value,
                        document_metadata=metadata_json,
                        content=pg_doc.content,
                        content_hash=pg_doc.content_hash,
                        search_space_id=pg_doc.search_space_id,
                        created_at=pg_doc.created_at,
                        updated_at=pg_doc.updated_at
                    )
                    
                    local_session.add(local_doc)
                    
                    # Prepare vector record if embedding exists
                    if pg_doc.embedding is not None:
                        # Convert pgvector to numpy array
                        embedding_array = np.array(pg_doc.embedding, dtype=np.float32)
                        
                        vector_record = VectorRecord(
                            id=pg_doc.id,
                            vector=embedding_array,
                            search_space_id=pg_doc.search_space_id,
                            record_type='document',
                            metadata={
                                'title': pg_doc.title,
                                'document_type': pg_doc.document_type.value,
                                'content_hash': pg_doc.content_hash,
                                'created_at': pg_doc.created_at.isoformat() if pg_doc.created_at else None
                            }
                        )
                        vector_records.append(vector_record)
                        self.migration_stats['embeddings']['migrated'] += 1
                    
                    self.migration_stats['documents']['migrated'] += 1
                    
                except Exception as e:
                    logger.error(f"Failed to migrate document {pg_doc.id}: {e}")
                    self.migration_stats['documents']['failed'] += 1
            
            await local_session.commit()
            offset += self.batch_size
            logger.info(f"Migrated {min(offset, total_docs)}/{total_docs} documents")
        
        # Batch insert vectors into FAISS
        if vector_records:
            logger.info(f"Adding {len(vector_records)} document embeddings to FAISS...")
            success = await local_db_service.faiss_manager.add_vectors(vector_records)
            if not success:
                logger.error("Failed to add some document embeddings to FAISS")
                self.migration_stats['embeddings']['failed'] += len(vector_records)
                self.migration_stats['embeddings']['migrated'] -= len(vector_records)
    
    async def _migrate_chunks(self, pg_session: AsyncSession, local_session: AsyncSession):
        """Migrate chunks and their embeddings"""
        logger.info("Migrating chunks...")
        
        result = await pg_session.execute(select(func.count(PgChunk.id)))
        total_chunks = result.scalar()
        self.migration_stats['chunks']['total'] = total_chunks
        
        vector_records = []
        
        offset = 0
        while offset < total_chunks:
            result = await pg_session.execute(
                select(PgChunk)
                .options(joinedload(PgChunk.document))
                .offset(offset)
                .limit(self.batch_size)
            )
            pg_chunks = result.scalars().all()
            
            for pg_chunk in pg_chunks:
                try:
                    local_chunk = LocalChunk(
                        id=pg_chunk.id,
                        content=pg_chunk.content,
                        document_id=pg_chunk.document_id,
                        created_at=pg_chunk.created_at,
                        updated_at=pg_chunk.updated_at
                    )
                    
                    local_session.add(local_chunk)
                    
                    # Prepare vector record if embedding exists
                    if pg_chunk.embedding is not None:
                        embedding_array = np.array(pg_chunk.embedding, dtype=np.float32)
                        
                        vector_record = VectorRecord(
                            id=pg_chunk.id,
                            vector=embedding_array,
                            search_space_id=pg_chunk.document.search_space_id,
                            record_type='chunk',
                            metadata={
                                'document_id': pg_chunk.document_id,
                                'document_title': pg_chunk.document.title,
                                'created_at': pg_chunk.created_at.isoformat() if pg_chunk.created_at else None
                            }
                        )
                        vector_records.append(vector_record)
                        self.migration_stats['embeddings']['migrated'] += 1
                    
                    self.migration_stats['chunks']['migrated'] += 1
                    
                except Exception as e:
                    logger.error(f"Failed to migrate chunk {pg_chunk.id}: {e}")
                    self.migration_stats['chunks']['failed'] += 1
            
            await local_session.commit()
            offset += self.batch_size
            logger.info(f"Migrated {min(offset, total_chunks)}/{total_chunks} chunks")
        
        # Batch insert vectors into FAISS
        if vector_records:
            logger.info(f"Adding {len(vector_records)} chunk embeddings to FAISS...")
            success = await local_db_service.faiss_manager.add_vectors(vector_records)
            if not success:
                logger.error("Failed to add some chunk embeddings to FAISS")
                self.migration_stats['embeddings']['failed'] += len(vector_records)
                self.migration_stats['embeddings']['migrated'] -= len(vector_records)

    async def _migrate_chats(self, pg_session: AsyncSession, local_session: AsyncSession):
        """Migrate chats"""
        logger.info("Migrating chats...")

        result = await pg_session.execute(select(func.count(PgChat.id)))
        total_chats = result.scalar()
        self.migration_stats['chats']['total'] = total_chats

        offset = 0
        while offset < total_chats:
            result = await pg_session.execute(
                select(PgChat)
                .offset(offset)
                .limit(self.batch_size)
            )
            pg_chats = result.scalars().all()

            for pg_chat in pg_chats:
                try:
                    # Convert arrays and JSON
                    initial_connectors_json = None
                    if pg_chat.initial_connectors:
                        initial_connectors_json = json.dumps(pg_chat.initial_connectors)

                    messages_json = json.dumps(pg_chat.messages) if pg_chat.messages else "[]"

                    local_chat = LocalChat(
                        id=pg_chat.id,
                        type=pg_chat.type.value,
                        title=pg_chat.title,
                        initial_connectors=initial_connectors_json,
                        messages=messages_json,
                        search_space_id=pg_chat.search_space_id,
                        created_at=pg_chat.created_at,
                        updated_at=pg_chat.updated_at
                    )

                    local_session.add(local_chat)
                    self.migration_stats['chats']['migrated'] += 1

                except Exception as e:
                    logger.error(f"Failed to migrate chat {pg_chat.id}: {e}")
                    self.migration_stats['chats']['failed'] += 1

            await local_session.commit()
            offset += self.batch_size
            logger.info(f"Migrated {min(offset, total_chats)}/{total_chats} chats")

    async def _migrate_podcasts(self, pg_session: AsyncSession, local_session: AsyncSession):
        """Migrate podcasts"""
        logger.info("Migrating podcasts...")

        result = await pg_session.execute(select(func.count(PgPodcast.id)))
        total_podcasts = result.scalar()
        self.migration_stats['podcasts']['total'] = total_podcasts

        offset = 0
        while offset < total_podcasts:
            result = await pg_session.execute(
                select(PgPodcast)
                .offset(offset)
                .limit(self.batch_size)
            )
            pg_podcasts = result.scalars().all()

            for pg_podcast in pg_podcasts:
                try:
                    transcript_json = json.dumps(pg_podcast.podcast_transcript) if pg_podcast.podcast_transcript else "{}"

                    local_podcast = LocalPodcast(
                        id=pg_podcast.id,
                        title=pg_podcast.title,
                        podcast_transcript=transcript_json,
                        file_location=pg_podcast.file_location,
                        search_space_id=pg_podcast.search_space_id,
                        created_at=pg_podcast.created_at,
                        updated_at=pg_podcast.updated_at
                    )

                    local_session.add(local_podcast)
                    self.migration_stats['podcasts']['migrated'] += 1

                except Exception as e:
                    logger.error(f"Failed to migrate podcast {pg_podcast.id}: {e}")
                    self.migration_stats['podcasts']['failed'] += 1

            await local_session.commit()
            offset += self.batch_size
            logger.info(f"Migrated {min(offset, total_podcasts)}/{total_podcasts} podcasts")

    async def _migrate_llm_configs(self, pg_session: AsyncSession, local_session: AsyncSession):
        """Migrate LLM configurations"""
        logger.info("Migrating LLM configurations...")

        result = await pg_session.execute(select(func.count(PgLLMConfig.id)))
        total_configs = result.scalar()
        self.migration_stats['llm_configs']['total'] = total_configs

        offset = 0
        while offset < total_configs:
            result = await pg_session.execute(
                select(PgLLMConfig)
                .offset(offset)
                .limit(self.batch_size)
            )
            pg_configs = result.scalars().all()

            for pg_config in pg_configs:
                try:
                    params_json = None
                    if pg_config.litellm_params:
                        params_json = json.dumps(pg_config.litellm_params)

                    local_config = LocalLLMConfig(
                        id=pg_config.id,
                        name=pg_config.name,
                        provider=pg_config.provider.value,
                        custom_provider=pg_config.custom_provider,
                        model_name=pg_config.model_name,
                        api_key=pg_config.api_key,
                        api_base=pg_config.api_base,
                        litellm_params=params_json,
                        user_id=str(pg_config.user_id),
                        created_at=pg_config.created_at,
                        updated_at=pg_config.updated_at
                    )

                    local_session.add(local_config)
                    self.migration_stats['llm_configs']['migrated'] += 1

                except Exception as e:
                    logger.error(f"Failed to migrate LLM config {pg_config.id}: {e}")
                    self.migration_stats['llm_configs']['failed'] += 1

            await local_session.commit()
            offset += self.batch_size
            logger.info(f"Migrated {min(offset, total_configs)}/{total_configs} LLM configs")

    async def _migrate_search_source_connectors(self, pg_session: AsyncSession, local_session: AsyncSession):
        """Migrate search source connectors"""
        logger.info("Migrating search source connectors...")

        result = await pg_session.execute(select(func.count(PgSearchSourceConnector.id)))
        total_connectors = result.scalar()
        self.migration_stats['connectors']['total'] = total_connectors

        offset = 0
        while offset < total_connectors:
            result = await pg_session.execute(
                select(PgSearchSourceConnector)
                .offset(offset)
                .limit(self.batch_size)
            )
            pg_connectors = result.scalars().all()

            for pg_connector in pg_connectors:
                try:
                    config_json = None
                    if pg_connector.additional_config:
                        config_json = json.dumps(pg_connector.additional_config)

                    local_connector = LocalSearchSourceConnector(
                        id=pg_connector.id,
                        name=pg_connector.name,
                        connector_type=pg_connector.connector_type.value,
                        api_key=pg_connector.api_key,
                        api_base=pg_connector.api_base,
                        additional_config=config_json,
                        user_id=str(pg_connector.user_id),
                        created_at=pg_connector.created_at,
                        updated_at=pg_connector.updated_at
                    )

                    local_session.add(local_connector)
                    self.migration_stats['connectors']['migrated'] += 1

                except Exception as e:
                    logger.error(f"Failed to migrate connector {pg_connector.id}: {e}")
                    self.migration_stats['connectors']['failed'] += 1

            await local_session.commit()
            offset += self.batch_size
            logger.info(f"Migrated {min(offset, total_connectors)}/{total_connectors} connectors")

    async def _create_backup(self):
        """Create backup of current local database"""
        backup_file = self.backup_dir / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        logger.info(f"Creating backup at {backup_file}")
        # Implementation would export current local data to JSON
        # This is a placeholder for the backup functionality
        pass

    async def _validate_migration(self, pg_session_maker) -> Dict[str, Any]:
        """Validate that migration was successful"""
        logger.info("Validating migration...")

        validation_report = {
            'data_integrity': True,
            'count_mismatches': [],
            'sample_checks': []
        }

        try:
            async with pg_session_maker() as pg_session:
                async with local_db_service.get_session() as local_session:
                    # Check record counts
                    tables_to_check = [
                        (PgUser, LocalUser, 'users'),
                        (PgSearchSpace, LocalSearchSpace, 'search_spaces'),
                        (PgDocument, LocalDocument, 'documents'),
                        (PgChunk, LocalChunk, 'chunks'),
                        (PgChat, LocalChat, 'chats'),
                        (PgPodcast, LocalPodcast, 'podcasts'),
                        (PgLLMConfig, LocalLLMConfig, 'llm_configs'),
                        (PgSearchSourceConnector, LocalSearchSourceConnector, 'connectors')
                    ]

                    for pg_model, local_model, table_name in tables_to_check:
                        pg_count = await pg_session.execute(select(func.count(pg_model.id)))
                        pg_count = pg_count.scalar()

                        local_count = await local_session.execute(select(func.count(local_model.id)))
                        local_count = local_count.scalar()

                        if pg_count != local_count:
                            validation_report['data_integrity'] = False
                            validation_report['count_mismatches'].append({
                                'table': table_name,
                                'postgresql_count': pg_count,
                                'local_count': local_count,
                                'difference': pg_count - local_count
                            })

                        logger.info(f"Validation - {table_name}: PG={pg_count}, Local={local_count}")

        except Exception as e:
            logger.error(f"Validation failed: {e}")
            validation_report['data_integrity'] = False
            validation_report['validation_error'] = str(e)

        return validation_report

# CLI interface for migration
async def run_migration():
    """Run the migration from command line"""
    migrator = DatabaseMigrator()
    report = await migrator.full_migration()

    print("\n" + "="*50)
    print("MIGRATION REPORT")
    print("="*50)
    print(f"Success: {report['success']}")
    if report['success']:
        print(f"Duration: {report['duration_seconds']:.2f} seconds")
    else:
        print(f"Error: {report.get('error', 'Unknown error')}")

    print("\nStatistics:")
    for table, stats in report['statistics'].items():
        if isinstance(stats, dict) and 'total' in stats:
            print(f"  {table}: {stats['migrated']}/{stats['total']} migrated, {stats['failed']} failed")

    return report

if __name__ == "__main__":
    asyncio.run(run_migration())
