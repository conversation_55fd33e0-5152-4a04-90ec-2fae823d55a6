"""
Performance monitoring and optimization for the local FAISS + SQLite database system.
Provides comprehensive metrics, automatic tuning, and performance comparisons.
"""

import time
import asyncio
import psutil
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import json
import numpy as np

from .config import db_mode_config, performance_monitor

logger = logging.getLogger(__name__)

@dataclass
class QueryMetrics:
    """Metrics for a single query"""
    query_type: str
    query_text: str
    duration_ms: float
    result_count: int
    memory_usage_mb: float
    cpu_usage_percent: float
    timestamp: datetime
    search_space_id: Optional[int] = None
    error: Optional[str] = None

@dataclass
class IndexMetrics:
    """Metrics for FAISS index operations"""
    operation: str  # 'add', 'search', 'rebuild', 'optimize'
    index_type: str
    vector_count: int
    duration_ms: float
    memory_usage_mb: float
    timestamp: datetime
    search_space_id: Optional[int] = None

@dataclass
class SystemMetrics:
    """Overall system performance metrics"""
    timestamp: datetime
    total_memory_mb: float
    available_memory_mb: float
    cpu_usage_percent: float
    disk_usage_mb: float
    active_indices: int
    total_vectors: int

class PerformanceProfiler:
    """Advanced performance profiler for database operations"""
    
    def __init__(self):
        self.query_metrics: List[QueryMetrics] = []
        self.index_metrics: List[IndexMetrics] = []
        self.system_metrics: List[SystemMetrics] = []
        self.max_metrics_history = 10000
        
        # Performance thresholds
        self.slow_query_threshold_ms = 1000
        self.memory_warning_threshold_mb = 1024
        self.cpu_warning_threshold_percent = 80
        
        # Auto-optimization settings
        self.enable_auto_optimization = True
        self.optimization_interval_minutes = 30
        self.last_optimization = datetime.now()
    
    def profile_query(self, query_type: str, query_text: str = "", search_space_id: Optional[int] = None):
        """Decorator to profile query performance"""
        def decorator(func: Callable):
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = self._get_memory_usage()
                start_cpu = psutil.cpu_percent()
                
                error = None
                result = None
                result_count = 0
                
                try:
                    result = await func(*args, **kwargs)
                    if isinstance(result, list):
                        result_count = len(result)
                    elif isinstance(result, dict) and 'results' in result:
                        result_count = len(result['results'])
                except Exception as e:
                    error = str(e)
                    raise
                finally:
                    end_time = time.time()
                    end_memory = self._get_memory_usage()
                    end_cpu = psutil.cpu_percent()
                    
                    duration_ms = (end_time - start_time) * 1000
                    memory_usage_mb = max(end_memory - start_memory, 0)
                    cpu_usage_percent = max(end_cpu - start_cpu, 0)
                    
                    metrics = QueryMetrics(
                        query_type=query_type,
                        query_text=query_text[:100],  # Truncate long queries
                        duration_ms=duration_ms,
                        result_count=result_count,
                        memory_usage_mb=memory_usage_mb,
                        cpu_usage_percent=cpu_usage_percent,
                        timestamp=datetime.now(),
                        search_space_id=search_space_id,
                        error=error
                    )
                    
                    self._record_query_metrics(metrics)
                    
                    # Check for performance issues
                    if duration_ms > self.slow_query_threshold_ms:
                        logger.warning(f"Slow query detected: {query_type} took {duration_ms:.2f}ms")
                    
                    if memory_usage_mb > self.memory_warning_threshold_mb:
                        logger.warning(f"High memory usage: {memory_usage_mb:.2f}MB for {query_type}")
                
                return result
            return wrapper
        return decorator
    
    def profile_index_operation(self, operation: str, index_type: str, search_space_id: Optional[int] = None):
        """Decorator to profile FAISS index operations"""
        def decorator(func: Callable):
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = self._get_memory_usage()
                
                result = await func(*args, **kwargs)
                
                end_time = time.time()
                end_memory = self._get_memory_usage()
                
                duration_ms = (end_time - start_time) * 1000
                memory_usage_mb = max(end_memory - start_memory, 0)
                
                # Try to get vector count from result or args
                vector_count = 0
                if isinstance(result, bool) and len(args) > 0:
                    # For add_vectors operations
                    if hasattr(args[0], '__len__'):
                        vector_count = len(args[0])
                
                metrics = IndexMetrics(
                    operation=operation,
                    index_type=index_type,
                    vector_count=vector_count,
                    duration_ms=duration_ms,
                    memory_usage_mb=memory_usage_mb,
                    timestamp=datetime.now(),
                    search_space_id=search_space_id
                )
                
                self._record_index_metrics(metrics)
                
                return result
            return wrapper
        return decorator
    
    def _record_query_metrics(self, metrics: QueryMetrics):
        """Record query performance metrics"""
        self.query_metrics.append(metrics)
        
        # Trim history if too long
        if len(self.query_metrics) > self.max_metrics_history:
            self.query_metrics = self.query_metrics[-self.max_metrics_history//2:]
        
        # Record in global performance monitor for compatibility
        performance_monitor.record_search_time(
            metrics.query_type, metrics.duration_ms / 1000, metrics.result_count
        )
    
    def _record_index_metrics(self, metrics: IndexMetrics):
        """Record index operation metrics"""
        self.index_metrics.append(metrics)
        
        # Trim history if too long
        if len(self.index_metrics) > self.max_metrics_history:
            self.index_metrics = self.index_metrics[-self.max_metrics_history//2:]
        
        # Record in global performance monitor
        performance_monitor.record_insert_time(
            metrics.operation, metrics.duration_ms / 1000, metrics.vector_count
        )
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def record_system_metrics(self):
        """Record current system metrics"""
        memory = psutil.virtual_memory()
        disk_usage = psutil.disk_usage('/')
        
        metrics = SystemMetrics(
            timestamp=datetime.now(),
            total_memory_mb=memory.total / 1024 / 1024,
            available_memory_mb=memory.available / 1024 / 1024,
            cpu_usage_percent=psutil.cpu_percent(),
            disk_usage_mb=disk_usage.used / 1024 / 1024,
            active_indices=0,  # Would be populated by FAISS manager
            total_vectors=0    # Would be populated by FAISS manager
        )
        
        self.system_metrics.append(metrics)
        
        # Trim history
        if len(self.system_metrics) > 1000:
            self.system_metrics = self.system_metrics[-500:]
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get performance summary for the last N hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Filter recent metrics
        recent_queries = [m for m in self.query_metrics if m.timestamp > cutoff_time]
        recent_index_ops = [m for m in self.index_metrics if m.timestamp > cutoff_time]
        recent_system = [m for m in self.system_metrics if m.timestamp > cutoff_time]
        
        summary = {
            'time_period_hours': hours,
            'query_performance': self._analyze_query_performance(recent_queries),
            'index_performance': self._analyze_index_performance(recent_index_ops),
            'system_performance': self._analyze_system_performance(recent_system),
            'recommendations': self._generate_recommendations(recent_queries, recent_index_ops)
        }
        
        return summary
    
    def _analyze_query_performance(self, queries: List[QueryMetrics]) -> Dict[str, Any]:
        """Analyze query performance metrics"""
        if not queries:
            return {'total_queries': 0}
        
        durations = [q.duration_ms for q in queries]
        result_counts = [q.result_count for q in queries]
        
        # Group by query type
        by_type = {}
        for query in queries:
            if query.query_type not in by_type:
                by_type[query.query_type] = []
            by_type[query.query_type].append(query.duration_ms)
        
        type_stats = {}
        for qtype, times in by_type.items():
            type_stats[qtype] = {
                'count': len(times),
                'avg_duration_ms': np.mean(times),
                'p95_duration_ms': np.percentile(times, 95),
                'p99_duration_ms': np.percentile(times, 99)
            }
        
        return {
            'total_queries': len(queries),
            'avg_duration_ms': np.mean(durations),
            'p95_duration_ms': np.percentile(durations, 95),
            'p99_duration_ms': np.percentile(durations, 99),
            'avg_result_count': np.mean(result_counts),
            'slow_queries': len([q for q in queries if q.duration_ms > self.slow_query_threshold_ms]),
            'error_rate': len([q for q in queries if q.error]) / len(queries),
            'by_type': type_stats
        }
    
    def _analyze_index_performance(self, operations: List[IndexMetrics]) -> Dict[str, Any]:
        """Analyze index operation performance"""
        if not operations:
            return {'total_operations': 0}
        
        durations = [op.duration_ms for op in operations]
        
        # Group by operation type
        by_operation = {}
        for op in operations:
            if op.operation not in by_operation:
                by_operation[op.operation] = []
            by_operation[op.operation].append(op.duration_ms)
        
        operation_stats = {}
        for op_type, times in by_operation.items():
            operation_stats[op_type] = {
                'count': len(times),
                'avg_duration_ms': np.mean(times),
                'total_vectors': sum(op.vector_count for op in operations if op.operation == op_type)
            }
        
        return {
            'total_operations': len(operations),
            'avg_duration_ms': np.mean(durations),
            'total_vectors_processed': sum(op.vector_count for op in operations),
            'by_operation': operation_stats
        }
    
    def _analyze_system_performance(self, system_metrics: List[SystemMetrics]) -> Dict[str, Any]:
        """Analyze system performance metrics"""
        if not system_metrics:
            return {'no_data': True}
        
        cpu_usage = [m.cpu_usage_percent for m in system_metrics]
        memory_usage = [m.total_memory_mb - m.available_memory_mb for m in system_metrics]
        
        return {
            'avg_cpu_usage_percent': np.mean(cpu_usage),
            'max_cpu_usage_percent': np.max(cpu_usage),
            'avg_memory_usage_mb': np.mean(memory_usage),
            'max_memory_usage_mb': np.max(memory_usage),
            'memory_usage_trend': 'increasing' if memory_usage[-1] > memory_usage[0] else 'stable'
        }
    
    def _generate_recommendations(self, queries: List[QueryMetrics], operations: List[IndexMetrics]) -> List[str]:
        """Generate performance optimization recommendations"""
        recommendations = []
        
        if queries:
            slow_queries = [q for q in queries if q.duration_ms > self.slow_query_threshold_ms]
            if len(slow_queries) > len(queries) * 0.1:  # More than 10% slow queries
                recommendations.append("Consider optimizing FAISS index parameters or increasing HNSW ef_search")
            
            high_memory_queries = [q for q in queries if q.memory_usage_mb > self.memory_warning_threshold_mb]
            if high_memory_queries:
                recommendations.append("High memory usage detected. Consider batch processing or index compression")
        
        if operations:
            add_operations = [op for op in operations if op.operation == 'add']
            if add_operations and np.mean([op.duration_ms for op in add_operations]) > 500:
                recommendations.append("Vector addition is slow. Consider batch insertions or index optimization")
        
        # Check system resources
        current_memory = psutil.virtual_memory()
        if current_memory.percent > 80:
            recommendations.append("System memory usage is high. Consider increasing available RAM")
        
        if not recommendations:
            recommendations.append("Performance is optimal. No recommendations at this time.")
        
        return recommendations
    
    def export_metrics(self, filepath: Path):
        """Export all metrics to JSON file"""
        data = {
            'export_timestamp': datetime.now().isoformat(),
            'query_metrics': [asdict(m) for m in self.query_metrics],
            'index_metrics': [asdict(m) for m in self.index_metrics],
            'system_metrics': [asdict(m) for m in self.system_metrics]
        }
        
        # Convert datetime objects to strings
        def convert_datetime(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            return obj
        
        with open(filepath, 'w') as f:
            json.dump(data, f, default=convert_datetime, indent=2)
        
        logger.info(f"Performance metrics exported to {filepath}")

# Global performance profiler instance
profiler = PerformanceProfiler()

# Automatic system metrics collection
async def start_system_monitoring():
    """Start background system metrics collection"""
    while True:
        try:
            profiler.record_system_metrics()
            await asyncio.sleep(60)  # Record every minute
        except Exception as e:
            logger.error(f"System monitoring error: {e}")
            await asyncio.sleep(60)

# Performance optimization functions
class IndexOptimizer:
    """Automatic index optimization based on performance metrics"""
    
    @staticmethod
    async def optimize_indices():
        """Optimize FAISS indices based on performance data"""
        try:
            from .service_adapters import local_db_service
            
            # Get performance data
            summary = profiler.get_performance_summary(hours=1)
            
            # Check if optimization is needed
            if summary['query_performance'].get('p95_duration_ms', 0) > 500:
                logger.info("Performance degradation detected, optimizing indices...")
                
                # This would trigger index optimization in FAISS manager
                # Implementation would depend on specific optimization strategies
                
                logger.info("Index optimization completed")
            
        except Exception as e:
            logger.error(f"Index optimization failed: {e}")

# Utility functions
def get_performance_report() -> Dict[str, Any]:
    """Get comprehensive performance report"""
    return profiler.get_performance_summary()

def export_performance_data(filepath: str):
    """Export performance data to file"""
    profiler.export_metrics(Path(filepath))

# Decorators for easy profiling
def profile_search(query_type: str, query_text: str = ""):
    """Decorator for profiling search operations"""
    return profiler.profile_query(query_type, query_text)

def profile_index_op(operation: str, index_type: str = "HNSW"):
    """Decorator for profiling index operations"""
    return profiler.profile_index_operation(operation, index_type)
