# Requirements for SurfSense Local Database (FAISS + SQLite)
# Optimized for performance and minimal dependencies

# Core FastAPI and async support
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database and ORM
sqlalchemy[asyncio]==2.0.23
aiosqlite==0.19.0
alembic==1.13.1

# Vector search and ML
faiss-cpu==1.7.4
# For GPU support, replace with: faiss-gpu==1.7.4
numpy==1.24.3
sentence-transformers==2.2.2

# HTTP client and async support
httpx==0.25.2
aiofiles==23.2.1
asyncio-mqtt==0.16.1

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Performance monitoring
psutil==5.9.6
prometheus-client==0.19.0

# Logging and configuration
python-dotenv==1.0.0
structlog==23.2.0
rich==13.7.0

# Data processing
pandas==2.1.4
orjson==3.9.10

# Optional: Redis for caching
redis==5.0.1
hiredis==2.2.3

# Development and testing (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Monitoring and observability
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0

# Additional utilities
click==8.1.7
tqdm==4.66.1
python-dateutil==2.8.2

# For embeddings model
torch==2.1.1+cpu --index-url https://download.pytorch.org/whl/cpu
transformers==4.36.0
tokenizers==0.15.0

# Optional: For advanced text processing
spacy==3.7.2
# python -m spacy download en_core_web_sm

# Optional: For document processing
pypdf2==3.0.1
python-docx==1.1.0
openpyxl==3.1.2

# Production server (alternative to uvicorn)
gunicorn==21.2.0

# Memory optimization
pympler==0.9
memory-profiler==0.61.0
