"""
Service adapters that provide the same interface as PostgreSQL services
but use the local FAISS + SQLite backend for superior performance.
"""

from typing import List, Dict, Any, Optional, AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, update
from sqlalchemy.orm import joinedload
import asyncio
import logging

from .sqlite_models import (
    LocalUser, LocalSearchSpace, LocalDocument, LocalChunk, 
    LocalChat, LocalPodcast, LocalLLMConfig, LocalSearchSourceConnector,
    get_local_session, async_session_maker
)
from .hybrid_search import LocalHybridSearchService
from .faiss_manager import FAISSIndexManager
from app.config import config

logger = logging.getLogger(__name__)

class LocalDatabaseService:
    """
    Main service class that provides the same interface as the PostgreSQL database
    but uses local FAISS + SQLite for superior performance.
    """
    
    def __init__(self):
        self.faiss_manager = FAISSIndexManager(
            embedding_dim=config.embedding_model_instance.dimension
        )
        self.search_service = LocalHybridSearchService(self.faiss_manager)
        self._initialized = False
    
    async def initialize(self):
        """Initialize the local database system"""
        if not self._initialized:
            from .sqlite_models import init_local_database
            await init_local_database()
            self._initialized = True
            logger.info("Local database system initialized")
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get async SQLite session - compatible with existing code"""
        if not self._initialized:
            await self.initialize()
        
        async with async_session_maker() as session:
            yield session
    
    # User operations
    async def create_user(self, session: AsyncSession, user_data: Dict[str, Any]) -> LocalUser:
        """Create a new user"""
        user = LocalUser(**user_data)
        session.add(user)
        await session.commit()
        await session.refresh(user)
        return user
    
    async def get_user_by_id(self, session: AsyncSession, user_id: str) -> Optional[LocalUser]:
        """Get user by ID"""
        result = await session.execute(
            select(LocalUser).where(LocalUser.id == user_id)
        )
        return result.scalars().first()
    
    async def get_user_by_email(self, session: AsyncSession, email: str) -> Optional[LocalUser]:
        """Get user by email"""
        result = await session.execute(
            select(LocalUser).where(LocalUser.email == email)
        )
        return result.scalars().first()
    
    # Search Space operations
    async def create_search_space(self, session: AsyncSession, search_space_data: Dict[str, Any]) -> LocalSearchSpace:
        """Create a new search space"""
        search_space = LocalSearchSpace(**search_space_data)
        session.add(search_space)
        await session.commit()
        await session.refresh(search_space)
        return search_space
    
    async def get_search_spaces_by_user(self, session: AsyncSession, user_id: str) -> List[LocalSearchSpace]:
        """Get all search spaces for a user"""
        result = await session.execute(
            select(LocalSearchSpace)
            .where(LocalSearchSpace.user_id == user_id)
            .order_by(LocalSearchSpace.created_at.desc())
        )
        return result.scalars().all()
    
    async def get_search_space_by_id(self, session: AsyncSession, search_space_id: int, user_id: str) -> Optional[LocalSearchSpace]:
        """Get search space by ID with ownership check"""
        result = await session.execute(
            select(LocalSearchSpace)
            .where(
                LocalSearchSpace.id == search_space_id,
                LocalSearchSpace.user_id == user_id
            )
        )
        return result.scalars().first()
    
    # Document operations
    async def create_document(self, session: AsyncSession, document_data: Dict[str, Any], 
                            embedding: Optional[Any] = None) -> LocalDocument:
        """Create a new document and add to search indices"""
        document = LocalDocument(**document_data)
        session.add(document)
        await session.commit()
        await session.refresh(document)
        
        # Add to search index
        await self.search_service.add_document(session, document, embedding)
        
        return document
    
    async def get_documents_by_search_space(self, session: AsyncSession, search_space_id: int, 
                                          skip: int = 0, limit: int = 100) -> List[LocalDocument]:
        """Get documents in a search space"""
        result = await session.execute(
            select(LocalDocument)
            .where(LocalDocument.search_space_id == search_space_id)
            .order_by(LocalDocument.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def get_document_by_id(self, session: AsyncSession, document_id: int) -> Optional[LocalDocument]:
        """Get document by ID"""
        result = await session.execute(
            select(LocalDocument)
            .options(joinedload(LocalDocument.search_space))
            .where(LocalDocument.id == document_id)
        )
        return result.scalars().first()
    
    async def update_document(self, session: AsyncSession, document_id: int, 
                            update_data: Dict[str, Any]) -> Optional[LocalDocument]:
        """Update a document"""
        result = await session.execute(
            select(LocalDocument).where(LocalDocument.id == document_id)
        )
        document = result.scalars().first()
        
        if document:
            for key, value in update_data.items():
                setattr(document, key, value)
            await session.commit()
            await session.refresh(document)
            
            # Update search index if content changed
            if 'content' in update_data:
                await self.search_service.add_document(session, document)
        
        return document
    
    async def delete_document(self, session: AsyncSession, document_id: int) -> bool:
        """Delete a document and remove from search indices"""
        result = await session.execute(
            select(LocalDocument).where(LocalDocument.id == document_id)
        )
        document = result.scalars().first()
        
        if document:
            # Remove from search index
            await self.search_service.remove_document(document_id, document.search_space_id)
            
            # Delete from database
            await session.delete(document)
            await session.commit()
            return True
        
        return False
    
    # Chunk operations
    async def create_chunk(self, session: AsyncSession, chunk_data: Dict[str, Any], 
                         embedding: Optional[Any] = None) -> LocalChunk:
        """Create a new chunk and add to search indices"""
        chunk = LocalChunk(**chunk_data)
        session.add(chunk)
        await session.commit()
        await session.refresh(chunk)
        
        # Add to search index
        await self.search_service.add_chunk(session, chunk, embedding)
        
        return chunk
    
    async def get_chunks_by_document(self, session: AsyncSession, document_id: int) -> List[LocalChunk]:
        """Get chunks for a document"""
        result = await session.execute(
            select(LocalChunk)
            .where(LocalChunk.document_id == document_id)
            .order_by(LocalChunk.id)
        )
        return result.scalars().all()
    
    async def delete_chunks_by_document(self, session: AsyncSession, document_id: int) -> bool:
        """Delete all chunks for a document"""
        # Get chunks first to remove from search index
        chunks = await self.get_chunks_by_document(session, document_id)
        
        if chunks:
            # Get search space ID from first chunk's document
            document = await self.get_document_by_id(session, document_id)
            if document:
                # Remove from search index
                chunk_ids = [chunk.id for chunk in chunks]
                for chunk_id in chunk_ids:
                    await self.search_service.remove_chunk(chunk_id, document.search_space_id)
        
        # Delete from database
        await session.execute(
            delete(LocalChunk).where(LocalChunk.document_id == document_id)
        )
        await session.commit()
        return True
    
    # Chat operations
    async def create_chat(self, session: AsyncSession, chat_data: Dict[str, Any]) -> LocalChat:
        """Create a new chat"""
        chat = LocalChat(**chat_data)
        session.add(chat)
        await session.commit()
        await session.refresh(chat)
        return chat
    
    async def get_chats_by_search_space(self, session: AsyncSession, search_space_id: int) -> List[LocalChat]:
        """Get chats in a search space"""
        result = await session.execute(
            select(LocalChat)
            .where(LocalChat.search_space_id == search_space_id)
            .order_by(LocalChat.created_at.desc())
        )
        return result.scalars().all()
    
    # Search operations - the main advantage of this system
    async def hybrid_search_documents(self, session: AsyncSession, query_text: str, 
                                    top_k: int, user_id: str, 
                                    search_space_id: Optional[int] = None,
                                    document_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Perform hybrid search on documents"""
        return await self.search_service.hybrid_search_documents(
            session, query_text, top_k, user_id, search_space_id, document_type
        )
    
    async def hybrid_search_chunks(self, session: AsyncSession, query_text: str, 
                                 top_k: int, user_id: str, 
                                 search_space_id: Optional[int] = None,
                                 document_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Perform hybrid search on chunks"""
        return await self.search_service.hybrid_search_chunks(
            session, query_text, top_k, user_id, search_space_id, document_type
        )
    
    async def vector_search_documents(self, query_text: str, top_k: int, user_id: str, 
                                    search_space_id: Optional[int] = None) -> List[LocalDocument]:
        """Perform vector-only search on documents"""
        return await self.search_service.vector_search_documents(
            query_text, top_k, user_id, search_space_id
        )
    
    async def vector_search_chunks(self, query_text: str, top_k: int, user_id: str, 
                                 search_space_id: Optional[int] = None) -> List[LocalChunk]:
        """Perform vector-only search on chunks"""
        return await self.search_service.vector_search_chunks(
            query_text, top_k, user_id, search_space_id
        )
    
    async def get_search_stats(self, search_space_id: Optional[int] = None) -> Dict[str, Any]:
        """Get search performance statistics"""
        return await self.search_service.get_search_stats(search_space_id)
    
    def cleanup(self):
        """Clean up resources"""
        self.faiss_manager.cleanup()

# Global instance
local_db_service = LocalDatabaseService()
