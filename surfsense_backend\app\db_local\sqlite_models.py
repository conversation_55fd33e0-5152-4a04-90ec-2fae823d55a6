"""
SQLite models that mirror the PostgreSQL schema.
Optimized for local performance with FTS5 for full-text search.
"""

import sqlite3
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from enum import Enum
from sqlalchemy import create_engine, Column, Integer, String, Text, JSON, Boolean, DateTime, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
import uuid
import json

Base = declarative_base()

# Enums matching the original schema
class DocumentType(str, Enum):
    FILE = "FILE"
    CRAWLED_URL = "CRAWLED_URL"

class ChatType(str, Enum):
    QUICK = "QUICK"
    DEEP = "DEEP"
    STRATEGIC = "STRATEGIC"

class LiteLLMProvider(str, Enum):
    OPENAI = "OPENAI"
    ANTHROPIC = "ANTHROPIC"
    GOOGLE = "GOOGLE"
    CUSTOM = "CUSTOM"

class SearchSourceConnectorType(str, Enum):
    SERPER_API = "SERPER_API"
    TAVILY_API = "TAVILY_API"
    SLACK_CONNECTOR = "SLACK_CONNECTOR"
    NOTION_CONNECTOR = "NOTION_CONNECTOR"
    GITHUB_CONNECTOR = "GITHUB_CONNECTOR"
    LINEAR_CONNECTOR = "LINEAR_CONNECTOR"
    DISCORD_CONNECTOR = "DISCORD_CONNECTOR"

class TimestampMixin:
    """Mixin for created_at and updated_at timestamps"""
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), 
                       onupdate=lambda: datetime.now(timezone.utc), nullable=False)

class LocalUser(Base, TimestampMixin):
    """Local SQLite user model"""
    __tablename__ = "users"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # LLM Configuration references
    long_context_llm_id = Column(Integer, ForeignKey("llm_configs.id"), nullable=True)
    fast_llm_id = Column(Integer, ForeignKey("llm_configs.id"), nullable=True)
    strategic_llm_id = Column(Integer, ForeignKey("llm_configs.id"), nullable=True)
    
    # Relationships
    search_spaces = relationship("LocalSearchSpace", back_populates="user", cascade="all, delete-orphan")
    search_source_connectors = relationship("LocalSearchSourceConnector", back_populates="user", cascade="all, delete-orphan")
    llm_configs = relationship("LocalLLMConfig", back_populates="user", foreign_keys="LocalLLMConfig.user_id", cascade="all, delete-orphan")

class LocalSearchSpace(Base, TimestampMixin):
    """Local SQLite search space model"""
    __tablename__ = "search_spaces"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(String(500), nullable=True)
    
    user_id = Column(String, ForeignKey("users.id", ondelete='CASCADE'), nullable=False)
    user = relationship("LocalUser", back_populates="search_spaces")
    
    # Relationships
    documents = relationship("LocalDocument", back_populates="search_space", cascade="all, delete-orphan")
    podcasts = relationship("LocalPodcast", back_populates="search_space", cascade="all, delete-orphan")
    chats = relationship("LocalChat", back_populates="search_space", cascade="all, delete-orphan")

class LocalDocument(Base, TimestampMixin):
    """Local SQLite document model"""
    __tablename__ = "documents"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String, nullable=False, index=True)
    document_type = Column(String, nullable=False)  # Store enum as string
    document_metadata = Column(Text, nullable=True)  # JSON as text
    
    content = Column(Text, nullable=False)
    content_hash = Column(String, nullable=False, index=True, unique=True)
    
    search_space_id = Column(Integer, ForeignKey("search_spaces.id", ondelete='CASCADE'), nullable=False)
    search_space = relationship("LocalSearchSpace", back_populates="documents")
    chunks = relationship("LocalChunk", back_populates="document", cascade="all, delete-orphan")
    
    def get_document_metadata(self) -> Dict[str, Any]:
        """Parse JSON metadata"""
        if self.document_metadata:
            try:
                return json.loads(self.document_metadata)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_document_metadata(self, metadata: Dict[str, Any]):
        """Set JSON metadata"""
        self.document_metadata = json.dumps(metadata) if metadata else None

class LocalChunk(Base, TimestampMixin):
    """Local SQLite chunk model"""
    __tablename__ = "chunks"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    content = Column(Text, nullable=False)
    
    document_id = Column(Integer, ForeignKey("documents.id", ondelete='CASCADE'), nullable=False)
    document = relationship("LocalDocument", back_populates="chunks")

class LocalPodcast(Base, TimestampMixin):
    """Local SQLite podcast model"""
    __tablename__ = "podcasts"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String, nullable=False, index=True)
    podcast_transcript = Column(Text, nullable=False, default='{}')  # JSON as text
    file_location = Column(String(500), nullable=False, default="")
    
    search_space_id = Column(Integer, ForeignKey("search_spaces.id", ondelete='CASCADE'), nullable=False)
    search_space = relationship("LocalSearchSpace", back_populates="podcasts")
    
    def get_podcast_transcript(self) -> Dict[str, Any]:
        """Parse JSON transcript"""
        try:
            return json.loads(self.podcast_transcript)
        except json.JSONDecodeError:
            return {}
    
    def set_podcast_transcript(self, transcript: Dict[str, Any]):
        """Set JSON transcript"""
        self.podcast_transcript = json.dumps(transcript)

class LocalChat(Base, TimestampMixin):
    """Local SQLite chat model"""
    __tablename__ = "chats"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    type = Column(String, nullable=False)  # Store enum as string
    title = Column(String, nullable=False, index=True)
    initial_connectors = Column(Text, nullable=True)  # JSON array as text
    messages = Column(Text, nullable=False)  # JSON as text
    
    search_space_id = Column(Integer, ForeignKey('search_spaces.id', ondelete='CASCADE'), nullable=False)
    search_space = relationship('LocalSearchSpace', back_populates='chats')
    
    def get_initial_connectors(self) -> List[str]:
        """Parse JSON initial_connectors"""
        if self.initial_connectors:
            try:
                return json.loads(self.initial_connectors)
            except json.JSONDecodeError:
                return []
        return []
    
    def set_initial_connectors(self, connectors: List[str]):
        """Set JSON initial_connectors"""
        self.initial_connectors = json.dumps(connectors) if connectors else None
    
    def get_messages(self) -> List[Dict[str, Any]]:
        """Parse JSON messages"""
        try:
            return json.loads(self.messages)
        except json.JSONDecodeError:
            return []
    
    def set_messages(self, messages: List[Dict[str, Any]]):
        """Set JSON messages"""
        self.messages = json.dumps(messages)

class LocalLLMConfig(Base, TimestampMixin):
    """Local SQLite LLM configuration model"""
    __tablename__ = "llm_configs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, index=True)
    provider = Column(String, nullable=False)  # Store enum as string
    custom_provider = Column(String(100), nullable=True)
    model_name = Column(String(100), nullable=False)
    api_key = Column(String, nullable=False)
    api_base = Column(String(500), nullable=True)
    litellm_params = Column(Text, nullable=True)  # JSON as text
    
    user_id = Column(String, ForeignKey("users.id", ondelete='CASCADE'), nullable=False)
    user = relationship("LocalUser", back_populates="llm_configs", foreign_keys=[user_id])
    
    def get_litellm_params(self) -> Dict[str, Any]:
        """Parse JSON litellm_params"""
        if self.litellm_params:
            try:
                return json.loads(self.litellm_params)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_litellm_params(self, params: Dict[str, Any]):
        """Set JSON litellm_params"""
        self.litellm_params = json.dumps(params) if params else None

class LocalSearchSourceConnector(Base, TimestampMixin):
    """Local SQLite search source connector model"""
    __tablename__ = "search_source_connectors"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, index=True)
    connector_type = Column(String, nullable=False)  # Store enum as string
    api_key = Column(String, nullable=False)
    api_base = Column(String(500), nullable=True)
    additional_config = Column(Text, nullable=True)  # JSON as text
    
    user_id = Column(String, ForeignKey("users.id", ondelete='CASCADE'), nullable=False)
    user = relationship("LocalUser", back_populates="search_source_connectors")
    
    def get_additional_config(self) -> Dict[str, Any]:
        """Parse JSON additional_config"""
        if self.additional_config:
            try:
                return json.loads(self.additional_config)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_additional_config(self, config: Dict[str, Any]):
        """Set JSON additional_config"""
        self.additional_config = json.dumps(config) if config else None

# Database setup
DATABASE_URL = "sqlite+aiosqlite:///./surfsense_local.db"

engine = create_async_engine(DATABASE_URL, echo=False)
async_session_maker = async_sessionmaker(engine, expire_on_commit=False)

async def get_local_session() -> AsyncSession:
    """Get async SQLite session"""
    async with async_session_maker() as session:
        yield session

async def init_local_database():
    """Initialize the local SQLite database with FTS5 support"""
    async with engine.begin() as conn:
        # Create all tables
        await conn.run_sync(Base.metadata.create_all)
        
        # Create FTS5 virtual tables for full-text search
        await conn.execute("""
            CREATE VIRTUAL TABLE IF NOT EXISTS documents_fts USING fts5(
                id UNINDEXED,
                content,
                title,
                content='documents',
                content_rowid='id'
            )
        """)
        
        await conn.execute("""
            CREATE VIRTUAL TABLE IF NOT EXISTS chunks_fts USING fts5(
                id UNINDEXED,
                content,
                content='chunks',
                content_rowid='id'
            )
        """)
        
        # Create triggers to keep FTS5 tables in sync
        await conn.execute("""
            CREATE TRIGGER IF NOT EXISTS documents_fts_insert AFTER INSERT ON documents BEGIN
                INSERT INTO documents_fts(id, content, title) VALUES (new.id, new.content, new.title);
            END
        """)
        
        await conn.execute("""
            CREATE TRIGGER IF NOT EXISTS documents_fts_delete AFTER DELETE ON documents BEGIN
                DELETE FROM documents_fts WHERE id = old.id;
            END
        """)
        
        await conn.execute("""
            CREATE TRIGGER IF NOT EXISTS documents_fts_update AFTER UPDATE ON documents BEGIN
                DELETE FROM documents_fts WHERE id = old.id;
                INSERT INTO documents_fts(id, content, title) VALUES (new.id, new.content, new.title);
            END
        """)
        
        await conn.execute("""
            CREATE TRIGGER IF NOT EXISTS chunks_fts_insert AFTER INSERT ON chunks BEGIN
                INSERT INTO chunks_fts(id, content) VALUES (new.id, new.content);
            END
        """)
        
        await conn.execute("""
            CREATE TRIGGER IF NOT EXISTS chunks_fts_delete AFTER DELETE ON chunks BEGIN
                DELETE FROM chunks_fts WHERE id = old.id;
            END
        """)
        
        await conn.execute("""
            CREATE TRIGGER IF NOT EXISTS chunks_fts_update AFTER UPDATE ON chunks BEGIN
                DELETE FROM chunks_fts WHERE id = old.id;
                INSERT INTO chunks_fts(id, content) VALUES (new.id, new.content);
            END
        """)
        
        await conn.commit()
